<template>
  <div class="h-full">
    <n-menu
      :collapsed="appStore.sidebarCollapsed"
      :collapsed-width="64"
      :options="menuOptions"
      :value="activeKey"
      @update:value="handleMenuSelect"
      class="h-full"
    />
  </div>
</template>

<script setup>
import { computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { NMenu, NIcon } from 'naive-ui'
import {
  HomeOutline,
  SettingsOutline,
  PeopleOutline,
  PersonCircleOutline,
  KeyOutline,
  DocumentTextOutline
} from '@vicons/ionicons5'
import { useAppStore } from '@/stores/app'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const appStore = useAppStore()

const activeKey = computed(() => {
  const path = route.path
  if (path.startsWith('/system')) {
    if (path.includes('/users')) return '/system/users'
    if (path.includes('/roles')) return '/system/roles'
    if (path.includes('/permissions')) return '/system/permissions'
    return '/system'
  }
  return path
})

const renderIcon = (icon) => {
  return () => h(NIcon, null, { default: () => h(icon) })
}

const menuOptions = computed(() => [
  {
    label: t('menu.dashboard'),
    key: '/dashboard',
    icon: renderIcon(HomeOutline)
  },
  {
    label: t('menu.system'),
    key: '/system',
    icon: renderIcon(SettingsOutline),
    children: [
      {
        label: t('menu.users'),
        key: '/system/users',
        icon: renderIcon(PeopleOutline)
      },
      {
        label: t('menu.roles'),
        key: '/system/roles',
        icon: renderIcon(PersonCircleOutline)
      },
      {
        label: t('menu.permissions'),
        key: '/system/permissions',
        icon: renderIcon(KeyOutline)
      }
    ]
  },
  {
    label: t('menu.logs'),
    key: '/logs',
    icon: renderIcon(DocumentTextOutline)
  }
])

const handleMenuSelect = (key) => {
  if (key && key !== route.path) {
    router.push(key)
  }
}
</script>
