'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'LogoSlack',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _cache[0] || (_cache[0] = [(0, vue_1.createStaticVNode)('<path d="M126.12 315.1A47.06 47.06 0 1 1 79.06 268h47.06z" fill="currentColor"></path><path d="M149.84 315.1a47.06 47.06 0 0 1 94.12 0v117.84a47.06 47.06 0 1 1-94.12 0z" fill="currentColor"></path><path d="M196.9 126.12A47.06 47.06 0 1 1 244 79.06v47.06z" fill="currentColor"></path><path d="M196.9 149.84a47.06 47.06 0 0 1 0 94.12H79.06a47.06 47.06 0 0 1 0-94.12z" fill="currentColor"></path><path d="M385.88 196.9a47.06 47.06 0 1 1 47.06 47.1h-47.06z" fill="currentColor"></path><path d="M362.16 196.9a47.06 47.06 0 0 1-94.12 0V79.06a47.06 47.06 0 1 1 94.12 0z" fill="currentColor"></path><path d="M315.1 385.88a47.06 47.06 0 1 1-47.1 47.06v-47.06z" fill="currentColor"></path><path d="M315.1 362.16a47.06 47.06 0 0 1 0-94.12h117.84a47.06 47.06 0 1 1 0 94.12z" fill="currentColor"></path>', 8)]))
  }
})
