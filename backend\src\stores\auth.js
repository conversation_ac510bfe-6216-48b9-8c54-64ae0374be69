import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login as apiLogin, logout as apiLogout, getUserInfo } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('token') || '')
  const user = ref(null)
  const permissions = ref([])

  const isAuthenticated = computed(() => !!token.value)

  const login = async (credentials) => {
    try {
      const response = await apiLogin(credentials)
      token.value = response.data.token
      user.value = response.data.user
      permissions.value = response.data.permissions || []
      
      localStorage.setItem('token', token.value)
      return response
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    try {
      await apiLogout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      token.value = ''
      user.value = null
      permissions.value = []
      localStorage.removeItem('token')
    }
  }

  const fetchUserInfo = async () => {
    try {
      const response = await getUserInfo()
      user.value = response.data.user
      permissions.value = response.data.permissions || []
      return response
    } catch (error) {
      throw error
    }
  }

  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }

  return {
    token,
    user,
    permissions,
    isAuthenticated,
    login,
    logout,
    fetchUserInfo,
    hasPermission
  }
})
