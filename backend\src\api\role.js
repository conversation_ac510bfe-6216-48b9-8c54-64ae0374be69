import request from './request'

// 获取角色列表
export const getRoleList = (params) => {
  return request({
    url: '/roles',
    method: 'get',
    params
  })
}

// 获取角色详情
export const getRoleDetail = (id) => {
  return request({
    url: `/roles/${id}`,
    method: 'get'
  })
}

// 创建角色
export const createRole = (data) => {
  return request({
    url: '/roles',
    method: 'post',
    data
  })
}

// 更新角色
export const updateRole = (id, data) => {
  return request({
    url: `/roles/${id}`,
    method: 'put',
    data
  })
}

// 删除角色
export const deleteRole = (id) => {
  return request({
    url: `/roles/${id}`,
    method: 'delete'
  })
}

// 获取角色权限
export const getRolePermissions = (id) => {
  return request({
    url: `/roles/${id}/permissions`,
    method: 'get'
  })
}

// 分配角色权限
export const assignRolePermissions = (id, permissions) => {
  return request({
    url: `/roles/${id}/permissions`,
    method: 'post',
    data: { permissions }
  })
}
