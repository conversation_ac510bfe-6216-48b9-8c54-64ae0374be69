import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/auth/Login.vue'),
      meta: { requiresAuth: false, title: '登录' }
    },
    {
      path: '/',
      name: 'Layout',
      component: () => import('@/components/layout/AppLayout.vue'),
      redirect: '/dashboard',
      meta: { requiresAuth: true },
      children: [
        {
          path: '/dashboard',
          name: 'Dashboard',
          component: () => import('@/views/dashboard/Dashboard.vue'),
          meta: {
            title: '分析页',
            icon: 'mdi:view-dashboard',
            requiresAuth: true
          }
        },
        {
          path: '/system',
          name: 'System',
          redirect: '/system/users',
          meta: {
            title: '系统管理',
            icon: 'mdi:cog',
            requiresAuth: true
          },
          children: [
            {
              path: '/system/users',
              name: 'Users',
              component: () => import('@/views/system/Users.vue'),
              meta: {
                title: '用户管理',
                icon: 'mdi:account-group',
                requiresAuth: true
              }
            },
            {
              path: '/system/roles',
              name: 'Roles',
              component: () => import('@/views/system/Roles.vue'),
              meta: {
                title: '角色管理',
                icon: 'mdi:account-star',
                requiresAuth: true
              }
            },
            {
              path: '/system/permissions',
              name: 'Permissions',
              component: () => import('@/views/system/Permissions.vue'),
              meta: {
                title: '权限管理',
                icon: 'mdi:key',
                requiresAuth: true
              }
            }
          ]
        },
        {
          path: '/logs',
          name: 'Logs',
          component: () => import('@/views/logs/Logs.vue'),
          meta: {
            title: '日志管理',
            icon: 'mdi:file-document-outline',
            requiresAuth: true
          }
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/error/404.vue'),
      meta: { title: '页面不存在' }
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Vue Admin`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!authStore.isAuthenticated) {
      next('/login')
      return
    }

    // 如果已登录但用户信息为空，尝试获取用户信息
    if (!authStore.user) {
      try {
        await authStore.getUserInfo()
      } catch (error) {
        await authStore.logout()
        next('/login')
        return
      }
    }
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
    return
  }

  next()
})

export default router
