<template>
  <el-container class="app-layout" :class="{ 'is-dark': appStore.isDark }">
    <!-- 侧边栏 -->
    <el-aside 
      :width="appStore.sidebarCollapsed ? '64px' : '240px'"
      class="app-aside"
    >
      <AppSidebar />
    </el-aside>

    <!-- 主体内容 -->
    <el-container class="app-main">
      <!-- 顶部导航 -->
      <el-header height="60px" class="app-header">
        <AppHeader />
      </el-header>

      <!-- 内容区域 -->
      <el-main class="app-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-slide" mode="out-in">
            <component :is="Component" :key="route.path" />
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { onMounted } from 'vue'
import { useAppStore } from '@/stores/app'
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'

const appStore = useAppStore()

onMounted(() => {
  appStore.initTheme()
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.app-aside {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-main {
  flex: 1;
  overflow: hidden;
}

.app-header {
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  padding: 0;
  display: flex;
  align-items: center;
}

.app-content {
  background: var(--el-bg-color-page);
  overflow-y: auto;
  padding: 20px;
}

/* 页面切换动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 深色模式 */
.is-dark {
  background: var(--el-bg-color);
}

.is-dark .app-aside {
  background: var(--el-bg-color);
  border-right-color: var(--el-border-color);
}

.is-dark .app-header {
  background: var(--el-bg-color);
  border-bottom-color: var(--el-border-color);
}

.is-dark .app-content {
  background: var(--el-bg-color-page);
}
</style>
