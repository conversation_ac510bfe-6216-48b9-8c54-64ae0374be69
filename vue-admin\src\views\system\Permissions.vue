<template>
  <div class="permissions-page">
    <div class="page-header">
      <h1>{{ $t('menu.permissions') }}</h1>
      <el-button type="primary">
        <el-icon><Plus /></el-icon>
        添加权限
      </el-button>
    </div>

    <el-card>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="权限名称" />
        <el-table-column prop="code" label="权限编码" />
        <el-table-column prop="type" label="权限类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeColor(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resource" label="资源" />
        <el-table-column prop="action" label="操作" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" text>
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" text>
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'

const { t } = useI18n()

const tableData = ref([])

const getTypeColor = (type) => {
  const colors = {
    menu: 'primary',
    button: 'success',
    api: 'warning'
  }
  return colors[type] || 'info'
}

const getTypeText = (type) => {
  const texts = {
    menu: '菜单',
    button: '按钮',
    api: 'API'
  }
  return texts[type] || type
}

const loadPermissions = () => {
  tableData.value = [
    {
      id: 1,
      name: '用户管理',
      code: 'user:manage',
      type: 'menu',
      resource: '/system/users',
      action: 'view',
      createTime: '2024-01-01 10:00:00'
    },
    {
      id: 2,
      name: '添加用户',
      code: 'user:create',
      type: 'button',
      resource: 'user',
      action: 'create',
      createTime: '2024-01-01 10:00:00'
    }
  ]
}

onMounted(() => {
  loadPermissions()
})
</script>

<style scoped>
.permissions-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}
</style>
