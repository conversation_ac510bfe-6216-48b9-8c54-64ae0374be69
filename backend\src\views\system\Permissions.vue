<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        {{ $t('menu.permissions') }}
      </h1>
      <n-button type="primary" @click="showAddModal = true">
        <template #icon>
          <n-icon><add-outline /></n-icon>
        </template>
        {{ $t('common.add') }}
      </n-button>
    </div>
    
    <!-- 搜索栏 -->
    <n-card>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <n-input
          v-model:value="searchForm.name"
          :placeholder="$t('permission.name')"
          clearable
        />
        <n-input
          v-model:value="searchForm.code"
          :placeholder="$t('permission.code')"
          clearable
        />
        <n-select
          v-model:value="searchForm.type"
          :placeholder="$t('permission.type')"
          :options="typeOptions"
          clearable
        />
        <div class="flex space-x-2">
          <n-button type="primary" @click="handleSearch">
            {{ $t('common.search') }}
          </n-button>
          <n-button @click="handleReset">
            {{ $t('common.reset') }}
          </n-button>
        </div>
      </div>
    </n-card>
    
    <!-- 权限表格 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
    
    <!-- 添加/编辑权限模态框 -->
    <n-modal v-model:show="showAddModal" preset="dialog" :title="modalTitle">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item :label="$t('permission.name')" path="name">
          <n-input v-model:value="formData.name" />
        </n-form-item>
        
        <n-form-item :label="$t('permission.code')" path="code">
          <n-input v-model:value="formData.code" />
        </n-form-item>
        
        <n-form-item :label="$t('permission.type')" path="type">
          <n-select
            v-model:value="formData.type"
            :options="typeOptions"
          />
        </n-form-item>
        
        <n-form-item :label="$t('permission.resource')" path="resource">
          <n-input v-model:value="formData.resource" />
        </n-form-item>
        
        <n-form-item :label="$t('permission.action')" path="action">
          <n-input v-model:value="formData.action" />
        </n-form-item>
        
        <n-form-item :label="$t('permission.description')" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            :rows="3"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <div class="flex justify-end space-x-2">
          <n-button @click="showAddModal = false">
            {{ $t('common.cancel') }}
          </n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ $t('common.confirm') }}
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  NCard,
  NButton,
  NIcon,
  NInput,
  NSelect,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NTag,
  useMessage,
  useDialog
} from 'naive-ui'
import {
  AddOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'

const { t } = useI18n()
const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const submitting = ref(false)
const showAddModal = ref(false)
const isEdit = ref(false)
const formRef = ref()

const searchForm = reactive({
  name: '',
  code: '',
  type: null
})

const formData = reactive({
  id: null,
  name: '',
  code: '',
  type: 'menu',
  resource: '',
  action: '',
  description: ''
})

const tableData = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

const modalTitle = computed(() => {
  return isEdit.value ? '编辑权限' : '添加权限'
})

const typeOptions = [
  { label: '菜单', value: 'menu' },
  { label: '按钮', value: 'button' },
  { label: 'API', value: 'api' }
]

const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: t('permission.name'),
    key: 'name'
  },
  {
    title: t('permission.code'),
    key: 'code'
  },
  {
    title: t('permission.type'),
    key: 'type',
    render(row) {
      const typeMap = {
        menu: { type: 'info', text: '菜单' },
        button: { type: 'success', text: '按钮' },
        api: { type: 'warning', text: 'API' }
      }
      const config = typeMap[row.type] || { type: 'default', text: row.type }
      return h(NTag, { type: config.type }, { default: () => config.text })
    }
  },
  {
    title: t('permission.resource'),
    key: 'resource'
  },
  {
    title: t('permission.action'),
    key: 'action'
  },
  {
    title: t('permission.description'),
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-right: 8px',
            onClick: () => handleEdit(row)
          },
          { default: () => '编辑', icon: () => h(NIcon, null, { default: () => h(CreateOutline) }) }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          },
          { default: () => '删除', icon: () => h(NIcon, null, { default: () => h(TrashOutline) }) }
        )
      ]
    }
  }
]

const rules = {
  name: [
    { required: true, message: '请输入权限名称', trigger: ['input', 'blur'] }
  ],
  code: [
    { required: true, message: '请输入权限编码', trigger: ['input', 'blur'] }
  ],
  type: [
    { required: true, message: '请选择权限类型', trigger: ['change', 'blur'] }
  ]
}

const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    tableData.value = [
      {
        id: 1,
        name: '用户管理',
        code: 'user:manage',
        type: 'menu',
        resource: '/system/users',
        action: 'view',
        description: '用户管理菜单权限'
      },
      {
        id: 2,
        name: '添加用户',
        code: 'user:create',
        type: 'button',
        resource: 'user',
        action: 'create',
        description: '添加用户按钮权限'
      },
      {
        id: 3,
        name: '用户列表API',
        code: 'api:user:list',
        type: 'api',
        resource: '/api/users',
        action: 'GET',
        description: '获取用户列表API权限'
      }
    ]
    
    pagination.itemCount = tableData.value.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    code: '',
    type: null
  })
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    code: row.code,
    type: row.type,
    resource: row.resource,
    action: row.action,
    description: row.description
  })
  showAddModal.value = true
}

const handleDelete = (row) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除权限 "${row.name}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(isEdit.value ? '更新成功' : '添加成功')
    showAddModal.value = false
    resetForm()
    loadData()
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '添加失败')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  isEdit.value = false
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    type: 'menu',
    resource: '',
    action: '',
    description: ''
  })
}

onMounted(() => {
  loadData()
})
</script>
