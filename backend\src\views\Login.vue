<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <n-config-provider :theme="isDark ? darkTheme : null">
      <div class="max-w-md w-full space-y-8 p-8">
        <div class="text-center">
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            {{ $t('login.title') }}
          </h2>
        </div>
        
        <n-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          @submit.prevent="handleSubmit"
        >
          <n-form-item path="username" :label="$t('login.username')">
            <n-input
              v-model:value="formData.username"
              :placeholder="$t('login.username')"
              size="large"
            />
          </n-form-item>
          
          <n-form-item path="password" :label="$t('login.password')">
            <n-input
              v-model:value="formData.password"
              type="password"
              :placeholder="$t('login.password')"
              size="large"
              @keydown.enter="handleSubmit"
            />
          </n-form-item>
          
          <n-form-item>
            <div class="flex items-center justify-between w-full">
              <n-checkbox v-model:checked="formData.remember">
                {{ $t('login.remember') }}
              </n-checkbox>
              
              <n-button text type="primary">
                {{ $t('login.forgot') }}
              </n-button>
            </div>
          </n-form-item>
          
          <n-form-item>
            <n-button
              type="primary"
              size="large"
              :loading="loading"
              :disabled="loading"
              @click="handleSubmit"
              class="w-full"
            >
              {{ $t('login.login') }}
            </n-button>
          </n-form-item>
        </n-form>
        
        <div class="text-center">
          <n-button quaternary @click="toggleTheme" class="mr-4">
            <template #icon>
              <n-icon>
                <sunny-outline v-if="isDark" />
                <moon-outline v-else />
              </n-icon>
            </template>
          </n-button>
          
          <n-dropdown :options="languageOptions" @select="handleLanguageSelect">
            <n-button quaternary>
              <template #icon>
                <n-icon><language-outline /></n-icon>
              </template>
            </n-button>
          </n-dropdown>
        </div>
      </div>
    </n-config-provider>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import {
  NConfigProvider,
  NForm,
  NFormItem,
  NInput,
  NButton,
  NCheckbox,
  NDropdown,
  NIcon,
  useMessage,
  darkTheme
} from 'naive-ui'
import {
  SunnyOutline,
  MoonOutline,
  LanguageOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'

const router = useRouter()
const { locale, t } = useI18n()
const message = useMessage()
const authStore = useAuthStore()
const appStore = useAppStore()

const formRef = ref()
const loading = ref(false)

const isDark = computed(() => appStore.theme === 'dark')

const formData = ref({
  username: '',
  password: '',
  remember: false
})

const rules = {
  username: [
    {
      required: true,
      message: '请输入用户名',
      trigger: ['input', 'blur']
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: ['input', 'blur']
    }
  ]
}

const languageOptions = [
  {
    label: '简体中文',
    key: 'zh-CN'
  },
  {
    label: 'English',
    key: 'en-US'
  }
]

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true
    
    await authStore.login(formData.value)
    message.success('登录成功')
    router.push('/dashboard')
  } catch (error) {
    console.error('Login error:', error)
    message.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

const toggleTheme = () => {
  appStore.toggleTheme()
}

const handleLanguageSelect = (key) => {
  locale.value = key
  appStore.setLocale(key)
}
</script>
