<template>
  <n-config-provider :theme="isDark ? darkTheme : null" :locale="zhCN" :date-locale="dateZhCN">
    <n-message-provider>
      <n-layout class="min-h-screen">
        <n-layout-header class="h-16 flex items-center justify-between px-6 border-b border-gray-200 dark:border-gray-700">
          <div class="flex items-center">
            <n-button quaternary @click="toggleSidebar" class="mr-4">
              <template #icon>
                <n-icon><menu-outline /></n-icon>
              </template>
            </n-button>
            <h1 class="text-xl font-bold text-gray-800 dark:text-white">管理后台</h1>
          </div>
          
          <div class="flex items-center space-x-4">
            <!-- 语言切换 -->
            <n-dropdown :options="languageOptions" @select="handleLanguageSelect">
              <n-button quaternary>
                <template #icon>
                  <n-icon><language-outline /></n-icon>
                </template>
              </n-button>
            </n-dropdown>
            
            <!-- 主题切换 -->
            <n-button quaternary @click="toggleTheme">
              <template #icon>
                <n-icon>
                  <sunny-outline v-if="isDark" />
                  <moon-outline v-else />
                </n-icon>
              </template>
            </n-button>
            
            <!-- 用户菜单 -->
            <n-dropdown :options="userMenuOptions" @select="handleUserMenuSelect">
              <n-button quaternary>
                <template #icon>
                  <n-icon><person-outline /></n-icon>
                </template>
                {{ authStore.user?.username || 'User' }}
              </n-button>
            </n-dropdown>
          </div>
        </n-layout-header>
        
        <n-layout has-sider class="flex-1">
          <n-layout-sider
            :collapsed="appStore.sidebarCollapsed"
            :collapsed-width="64"
            :width="240"
            show-trigger
            collapse-mode="width"
            bordered
            class="bg-white dark:bg-gray-800"
          >
            <SidebarMenu />
          </n-layout-sider>
          
          <n-layout-content class="p-6 bg-gray-50 dark:bg-gray-900">
            <router-view />
          </n-layout-content>
        </n-layout>
      </n-layout>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { 
  NConfigProvider, 
  NLayout, 
  NLayoutHeader, 
  NLayoutSider, 
  NLayoutContent,
  NButton,
  NIcon,
  NDropdown,
  NMessageProvider,
  darkTheme,
  zhCN,
  dateZhCN
} from 'naive-ui'
import {
  MenuOutline,
  PersonOutline,
  LanguageOutline,
  SunnyOutline,
  MoonOutline,
  LogOutOutline
} from '@vicons/ionicons5'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import SidebarMenu from './SidebarMenu.vue'

const router = useRouter()
const { locale } = useI18n()
const authStore = useAuthStore()
const appStore = useAppStore()

const isDark = computed(() => appStore.theme === 'dark')

const toggleSidebar = () => {
  appStore.toggleSidebar()
}

const toggleTheme = () => {
  appStore.toggleTheme()
}

const languageOptions = [
  {
    label: '简体中文',
    key: 'zh-CN'
  },
  {
    label: 'English',
    key: 'en-US'
  }
]

const userMenuOptions = [
  {
    label: '退出登录',
    key: 'logout',
    icon: () => h(NIcon, null, { default: () => h(LogOutOutline) })
  }
]

const handleLanguageSelect = (key) => {
  locale.value = key
  appStore.setLocale(key)
}

const handleUserMenuSelect = (key) => {
  if (key === 'logout') {
    authStore.logout()
    router.push('/login')
  }
}
</script>
