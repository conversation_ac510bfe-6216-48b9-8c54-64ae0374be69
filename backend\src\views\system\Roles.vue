<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        {{ $t('menu.roles') }}
      </h1>
      <n-button type="primary" @click="showAddModal = true">
        <template #icon>
          <n-icon><add-outline /></n-icon>
        </template>
        {{ $t('common.add') }}
      </n-button>
    </div>
    
    <!-- 搜索栏 -->
    <n-card>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <n-input
          v-model:value="searchForm.name"
          :placeholder="$t('role.name')"
          clearable
        />
        <n-input
          v-model:value="searchForm.code"
          :placeholder="$t('role.code')"
          clearable
        />
        <div class="flex space-x-2">
          <n-button type="primary" @click="handleSearch">
            {{ $t('common.search') }}
          </n-button>
          <n-button @click="handleReset">
            {{ $t('common.reset') }}
          </n-button>
        </div>
      </div>
    </n-card>
    
    <!-- 角色表格 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
    
    <!-- 添加/编辑角色模态框 -->
    <n-modal v-model:show="showAddModal" preset="dialog" :title="modalTitle" style="width: 600px">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item :label="$t('role.name')" path="name">
          <n-input v-model:value="formData.name" />
        </n-form-item>
        
        <n-form-item :label="$t('role.code')" path="code">
          <n-input v-model:value="formData.code" />
        </n-form-item>
        
        <n-form-item :label="$t('role.description')" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            :rows="3"
          />
        </n-form-item>
        
        <n-form-item :label="$t('role.permissions')" path="permissions">
          <n-tree
            v-model:checked-keys="formData.permissions"
            :data="permissionTree"
            checkable
            cascade
            check-strategy="child"
            key-field="id"
            label-field="name"
            children-field="children"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <div class="flex justify-end space-x-2">
          <n-button @click="showAddModal = false">
            {{ $t('common.cancel') }}
          </n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ $t('common.confirm') }}
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  NCard,
  NButton,
  NIcon,
  NInput,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NTree,
  NTag,
  useMessage,
  useDialog
} from 'naive-ui'
import {
  AddOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'

const { t } = useI18n()
const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const submitting = ref(false)
const showAddModal = ref(false)
const isEdit = ref(false)
const formRef = ref()

const searchForm = reactive({
  name: '',
  code: ''
})

const formData = reactive({
  id: null,
  name: '',
  code: '',
  description: '',
  permissions: []
})

const tableData = ref([])
const permissionTree = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

const modalTitle = computed(() => {
  return isEdit.value ? '编辑角色' : '添加角色'
})

const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: t('role.name'),
    key: 'name'
  },
  {
    title: t('role.code'),
    key: 'code'
  },
  {
    title: t('role.description'),
    key: 'description'
  },
  {
    title: t('role.permissions'),
    key: 'permissions',
    render(row) {
      return h('div', { class: 'flex flex-wrap gap-1' }, 
        row.permissions?.slice(0, 3).map(permission => 
          h(NTag, { size: 'small', type: 'info' }, { default: () => permission.name })
        ).concat(
          row.permissions?.length > 3 ? 
            [h(NTag, { size: 'small' }, { default: () => `+${row.permissions.length - 3}` })] : 
            []
        )
      )
    }
  },
  {
    title: t('role.createTime'),
    key: 'created_at',
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-right: 8px',
            onClick: () => handleEdit(row)
          },
          { default: () => '编辑', icon: () => h(NIcon, null, { default: () => h(CreateOutline) }) }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          },
          { default: () => '删除', icon: () => h(NIcon, null, { default: () => h(TrashOutline) }) }
        )
      ]
    }
  }
]

const rules = {
  name: [
    { required: true, message: '请输入角色名称', trigger: ['input', 'blur'] }
  ],
  code: [
    { required: true, message: '请输入角色编码', trigger: ['input', 'blur'] }
  ]
}

const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    tableData.value = [
      {
        id: 1,
        name: '超级管理员',
        code: 'super_admin',
        description: '拥有所有权限的超级管理员',
        permissions: [
          { id: 1, name: '用户管理' },
          { id: 2, name: '角色管理' },
          { id: 3, name: '权限管理' }
        ],
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '普通用户',
        code: 'user',
        description: '普通用户角色',
        permissions: [
          { id: 4, name: '查看个人信息' }
        ],
        created_at: '2024-01-02T00:00:00Z'
      }
    ]
    
    pagination.itemCount = tableData.value.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const loadPermissions = async () => {
  try {
    // 模拟权限树数据
    permissionTree.value = [
      {
        id: 1,
        name: '系统管理',
        children: [
          { id: 2, name: '用户管理' },
          { id: 3, name: '角色管理' },
          { id: 4, name: '权限管理' }
        ]
      },
      {
        id: 5,
        name: '日志管理',
        children: [
          { id: 6, name: '查看日志' },
          { id: 7, name: '删除日志' }
        ]
      }
    ]
  } catch (error) {
    message.error('加载权限数据失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    code: ''
  })
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    name: row.name,
    code: row.code,
    description: row.description,
    permissions: row.permissions?.map(p => p.id) || []
  })
  showAddModal.value = true
}

const handleDelete = (row) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除角色 "${row.name}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(isEdit.value ? '更新成功' : '添加成功')
    showAddModal.value = false
    resetForm()
    loadData()
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '添加失败')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  isEdit.value = false
  Object.assign(formData, {
    id: null,
    name: '',
    code: '',
    description: '',
    permissions: []
  })
}

onMounted(() => {
  loadData()
  loadPermissions()
})
</script>
