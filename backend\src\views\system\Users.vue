<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        {{ $t('menu.users') }}
      </h1>
      <n-button type="primary" @click="showAddModal = true">
        <template #icon>
          <n-icon><add-outline /></n-icon>
        </template>
        {{ $t('common.add') }}
      </n-button>
    </div>
    
    <!-- 搜索栏 -->
    <n-card>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <n-input
          v-model:value="searchForm.username"
          :placeholder="$t('user.username')"
          clearable
        />
        <n-input
          v-model:value="searchForm.email"
          :placeholder="$t('user.email')"
          clearable
        />
        <n-select
          v-model:value="searchForm.status"
          :placeholder="$t('user.status')"
          :options="statusOptions"
          clearable
        />
        <div class="flex space-x-2">
          <n-button type="primary" @click="handleSearch">
            {{ $t('common.search') }}
          </n-button>
          <n-button @click="handleReset">
            {{ $t('common.reset') }}
          </n-button>
        </div>
      </div>
    </n-card>
    
    <!-- 用户表格 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
    
    <!-- 添加/编辑用户模态框 -->
    <n-modal v-model:show="showAddModal" preset="dialog" :title="modalTitle">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="auto"
      >
        <n-form-item :label="$t('user.username')" path="username">
          <n-input v-model:value="formData.username" />
        </n-form-item>
        
        <n-form-item :label="$t('user.email')" path="email">
          <n-input v-model:value="formData.email" />
        </n-form-item>
        
        <n-form-item :label="$t('user.phone')" path="phone">
          <n-input v-model:value="formData.phone" />
        </n-form-item>
        
        <n-form-item v-if="!isEdit" :label="$t('login.password')" path="password">
          <n-input v-model:value="formData.password" type="password" />
        </n-form-item>
        
        <n-form-item :label="$t('user.role')" path="roleId">
          <n-select
            v-model:value="formData.roleId"
            :options="roleOptions"
            :placeholder="$t('user.role')"
          />
        </n-form-item>
        
        <n-form-item :label="$t('user.status')" path="status">
          <n-select
            v-model:value="formData.status"
            :options="statusOptions"
          />
        </n-form-item>
      </n-form>
      
      <template #action>
        <div class="flex justify-end space-x-2">
          <n-button @click="showAddModal = false">
            {{ $t('common.cancel') }}
          </n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ $t('common.confirm') }}
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  NCard,
  NButton,
  NIcon,
  NInput,
  NSelect,
  NDataTable,
  NModal,
  NForm,
  NFormItem,
  NTag,
  useMessage,
  useDialog
} from 'naive-ui'
import {
  AddOutline,
  CreateOutline,
  TrashOutline
} from '@vicons/ionicons5'

const { t } = useI18n()
const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const submitting = ref(false)
const showAddModal = ref(false)
const isEdit = ref(false)
const formRef = ref()

const searchForm = reactive({
  username: '',
  email: '',
  status: null
})

const formData = reactive({
  id: null,
  username: '',
  email: '',
  phone: '',
  password: '',
  roleId: null,
  status: 1
})

const tableData = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

const modalTitle = computed(() => {
  return isEdit.value ? '编辑用户' : '添加用户'
})

const statusOptions = [
  { label: t('user.active'), value: 1 },
  { label: t('user.inactive'), value: 0 }
]

const roleOptions = ref([
  { label: '管理员', value: 1 },
  { label: '普通用户', value: 2 }
])

const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: t('user.username'),
    key: 'username'
  },
  {
    title: t('user.email'),
    key: 'email'
  },
  {
    title: t('user.phone'),
    key: 'phone'
  },
  {
    title: t('user.role'),
    key: 'role',
    render(row) {
      return row.role?.name || '-'
    }
  },
  {
    title: t('user.status'),
    key: 'status',
    render(row) {
      return h(
        NTag,
        {
          type: row.status === 1 ? 'success' : 'error'
        },
        {
          default: () => row.status === 1 ? t('user.active') : t('user.inactive')
        }
      )
    }
  },
  {
    title: t('user.createTime'),
    key: 'created_at',
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            style: 'margin-right: 8px',
            onClick: () => handleEdit(row)
          },
          { default: () => '编辑', icon: () => h(NIcon, null, { default: () => h(CreateOutline) }) }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          },
          { default: () => '删除', icon: () => h(NIcon, null, { default: () => h(TrashOutline) }) }
        )
      ]
    }
  }
]

const rules = computed(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: ['input', 'blur'] }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: ['input', 'blur'] },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: ['input', 'blur'] }
  ],
  password: isEdit.value ? [] : [
    { required: true, message: '请输入密码', trigger: ['input', 'blur'] }
  ],
  roleId: [
    { required: true, message: '请选择角色', trigger: ['change', 'blur'] }
  ]
}))

const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    tableData.value = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        phone: '13800138000',
        status: 1,
        role: { name: '管理员' },
        created_at: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        username: 'user1',
        email: '<EMAIL>',
        phone: '13800138001',
        status: 1,
        role: { name: '普通用户' },
        created_at: '2024-01-02T00:00:00Z'
      }
    ]
    
    pagination.itemCount = tableData.value.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    username: '',
    email: '',
    status: null
  })
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

const handleEdit = (row) => {
  isEdit.value = true
  Object.assign(formData, {
    id: row.id,
    username: row.username,
    email: row.email,
    phone: row.phone,
    roleId: row.role?.id,
    status: row.status
  })
  showAddModal.value = true
}

const handleDelete = (row) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除用户 "${row.username}" 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 500))
        message.success('删除成功')
        loadData()
      } catch (error) {
        message.error('删除失败')
      }
    }
  })
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    message.success(isEdit.value ? '更新成功' : '添加成功')
    showAddModal.value = false
    resetForm()
    loadData()
  } catch (error) {
    message.error(isEdit.value ? '更新失败' : '添加失败')
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  isEdit.value = false
  Object.assign(formData, {
    id: null,
    username: '',
    email: '',
    phone: '',
    password: '',
    roleId: null,
    status: 1
  })
}

onMounted(() => {
  loadData()
})
</script>
