import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import pinia from './stores'
import i18n from './locales'
import naive from 'naive-ui'
import { useAppStore } from './stores/app'

const app = createApp(App)

app.use(pinia)
app.use(router)
app.use(i18n)
app.use(naive)

// 初始化主题
const appStore = useAppStore()
appStore.initTheme()

app.mount('#app')
