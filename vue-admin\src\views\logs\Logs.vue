<template>
  <div class="logs-page">
    <div class="page-header">
      <h1>{{ $t('menu.logs') }}</h1>
      <el-button type="danger">
        <el-icon><Delete /></el-icon>
        清空日志
      </el-button>
    </div>

    <el-card>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="user" label="用户" width="120" />
        <el-table-column prop="action" label="操作" width="100">
          <template #default="{ row }">
            <el-tag :type="getActionColor(row.action)" size="small">
              {{ getActionText(row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resource" label="资源" />
        <el-table-column prop="ip" label="IP地址" width="140" />
        <el-table-column prop="createTime" label="操作时间" width="180" />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="primary" size="small" text>
              <el-icon><View /></el-icon>
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Delete, View } from '@element-plus/icons-vue'

const { t } = useI18n()

const tableData = ref([])

const getActionColor = (action) => {
  const colors = {
    login: 'success',
    logout: 'info',
    create: 'primary',
    update: 'warning',
    delete: 'danger'
  }
  return colors[action] || 'info'
}

const getActionText = (action) => {
  const texts = {
    login: '登录',
    logout: '登出',
    create: '创建',
    update: '更新',
    delete: '删除'
  }
  return texts[action] || action
}

const loadLogs = () => {
  tableData.value = [
    {
      id: 1,
      user: 'admin',
      action: 'login',
      resource: '系统',
      ip: '*************',
      createTime: '2024-01-01 10:00:00'
    },
    {
      id: 2,
      user: 'admin',
      action: 'create',
      resource: '用户',
      ip: '*************',
      createTime: '2024-01-01 10:05:00'
    }
  ]
}

onMounted(() => {
  loadLogs()
})
</script>

<style scoped>
.logs-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}
</style>
