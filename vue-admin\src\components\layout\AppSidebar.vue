<template>
  <div class="app-sidebar">
    <!-- Logo区域 -->
    <div class="sidebar-logo" :class="{ 'collapsed': appStore.sidebarCollapsed }">
      <div class="logo-container">
        <Icon icon="mdi:triangle" class="logo-icon" />
        <span v-if="!appStore.sidebarCollapsed" class="logo-text">Vue Admin</span>
      </div>
    </div>

    <!-- 菜单区域 -->
    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.sidebarCollapsed"
      :unique-opened="true"
      router
      class="sidebar-menu"
      @select="handleMenuSelect"
    >
      <!-- 首页 -->
      <el-menu-item index="/dashboard" class="menu-item">
        <el-icon>
          <Icon icon="mdi:view-dashboard" />
        </el-icon>
        <template #title>
          <span>{{ $t('menu.dashboard') }}</span>
        </template>
      </el-menu-item>

      <!-- 系统管理 -->
      <el-sub-menu index="/system" class="menu-item">
        <template #title>
          <el-icon>
            <Icon icon="mdi:cog" />
          </el-icon>
          <span>{{ $t('menu.system') }}</span>
        </template>
        
        <!-- 用户管理 -->
        <el-menu-item index="/system/users" class="sub-menu-item">
          <el-icon>
            <Icon icon="mdi:account-group" />
          </el-icon>
          <template #title>
            <span>{{ $t('menu.users') }}</span>
          </template>
        </el-menu-item>
        
        <!-- 角色管理 -->
        <el-menu-item index="/system/roles" class="sub-menu-item">
          <el-icon>
            <Icon icon="mdi:account-star" />
          </el-icon>
          <template #title>
            <span>{{ $t('menu.roles') }}</span>
          </template>
        </el-menu-item>
        
        <!-- 权限管理 -->
        <el-menu-item index="/system/permissions" class="sub-menu-item">
          <el-icon>
            <Icon icon="mdi:key" />
          </el-icon>
          <template #title>
            <span>{{ $t('menu.permissions') }}</span>
          </template>
        </el-menu-item>
      </el-sub-menu>

      <!-- 日志管理 -->
      <el-menu-item index="/logs" class="menu-item">
        <el-icon>
          <Icon icon="mdi:file-document-outline" />
        </el-icon>
        <template #title>
          <span>{{ $t('menu.logs') }}</span>
        </template>
      </el-menu-item>
    </el-menu>

    <!-- 收缩展开按钮 -->
    <div class="sidebar-toggle" @click="appStore.toggleSidebar">
      <el-icon>
        <Icon :icon="appStore.sidebarCollapsed ? 'mdi:chevron-right' : 'mdi:chevron-left'" />
      </el-icon>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/stores/app'

const route = useRoute()
const { t } = useI18n()
const appStore = useAppStore()

// 当前激活的菜单
const activeMenu = computed(() => {
  const path = route.path
  
  // 处理系统管理子菜单
  if (path.startsWith('/system/')) {
    return path
  }
  
  // 其他菜单
  return path
})

// 菜单选择处理
const handleMenuSelect = (index) => {
  console.log('Selected menu:', index)
}
</script>

<style scoped>
.app-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--el-bg-color);
}

/* Logo区域 */
.sidebar-logo {
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.sidebar-logo.collapsed {
  padding: 0 12px;
  justify-content: center;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 24px;
  color: var(--el-color-primary);
  flex-shrink: 0;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
}

/* 菜单区域 */
.sidebar-menu {
  border-right: none;
  flex: 1;
  overflow-y: auto;
}

.menu-item {
  margin: 4px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.sub-menu-item {
  margin: 2px 12px;
  border-radius: 4px;
}

/* 菜单项悬停效果 */
.sidebar-menu .el-menu-item:hover,
.sidebar-menu .el-sub-menu__title:hover {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

/* 激活状态 */
.sidebar-menu .el-menu-item.is-active {
  background-color: var(--el-color-primary);
  color: white;
  font-weight: 600;
}

/* 图标样式 */
.sidebar-menu .el-icon {
  width: 20px;
  height: 20px;
  font-size: 18px;
}

/* 折叠状态下的样式 */
.sidebar-menu.el-menu--collapse {
  width: 64px;
}

.sidebar-menu.el-menu--collapse .el-menu-item,
.sidebar-menu.el-menu--collapse .el-sub-menu {
  text-align: center;
}

.sidebar-menu.el-menu--collapse .menu-item {
  margin: 4px 8px;
}

/* 子菜单样式 */
.el-sub-menu .el-menu-item {
  padding-left: 50px !important;
  min-height: 40px;
  margin: 2px 12px;
  border-radius: 4px;
}

/* 收缩展开按钮 */
.sidebar-toggle {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid var(--el-border-color-light);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--el-text-color-regular);
  margin-top: auto;
}

.sidebar-toggle:hover {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

/* 暗色主题适配 */
.is-dark .sidebar-menu {
  background-color: var(--el-bg-color);
}

.is-dark .sidebar-menu .el-menu-item:hover,
.is-dark .sidebar-menu .el-sub-menu__title:hover {
  background-color: var(--el-color-primary-dark-2);
}

.is-dark .sidebar-menu .el-menu-item.is-active {
  background-color: var(--el-color-primary);
  color: #fff;
}

/* 滚动条样式 */
.app-sidebar::-webkit-scrollbar {
  width: 6px;
}

.app-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.app-sidebar::-webkit-scrollbar-thumb {
  background: var(--el-border-color-light);
  border-radius: 3px;
}

.app-sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color);
}
</style>
