import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 主题设置
  const theme = ref(localStorage.getItem('theme') || 'light')
  const isDark = computed(() => theme.value === 'dark')
  
  // 语言设置
  const locale = ref(localStorage.getItem('locale') || 'zh-CN')
  
  // 侧边栏设置
  const sidebarCollapsed = ref(JSON.parse(localStorage.getItem('sidebarCollapsed') || 'false'))
  const sidebarHidden = ref(JSON.parse(localStorage.getItem('sidebarHidden') || 'false'))
  
  // 布局设置
  const layoutSettings = ref({
    showHeader: true,
    showSidebar: true,
    showFooter: false,
    fixedHeader: true,
    fixedSidebar: true
  })

  // 设置主题
  const setTheme = (newTheme) => {
    theme.value = newTheme
    localStorage.setItem('theme', newTheme)
    
    // 更新HTML类名
    const html = document.documentElement
    if (newTheme === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  // 切换主题
  const toggleTheme = () => {
    setTheme(theme.value === 'light' ? 'dark' : 'light')
  }

  // 设置语言
  const setLocale = (newLocale) => {
    locale.value = newLocale
    localStorage.setItem('locale', newLocale)
  }

  // 切换侧边栏
  const toggleSidebar = () => {
    if (!sidebarHidden.value && !sidebarCollapsed.value) {
      // 展开状态 -> 收缩状态
      sidebarCollapsed.value = true
    } else if (!sidebarHidden.value && sidebarCollapsed.value) {
      // 收缩状态 -> 隐藏状态
      sidebarHidden.value = true
      sidebarCollapsed.value = false
    } else {
      // 隐藏状态 -> 展开状态
      sidebarHidden.value = false
      sidebarCollapsed.value = false
    }

    localStorage.setItem('sidebarCollapsed', JSON.stringify(sidebarCollapsed.value))
    localStorage.setItem('sidebarHidden', JSON.stringify(sidebarHidden.value))
  }

  // 设置侧边栏状态
  const setSidebarCollapsed = (collapsed) => {
    sidebarCollapsed.value = collapsed
    localStorage.setItem('sidebarCollapsed', JSON.stringify(collapsed))
  }

  // 设置侧边栏隐藏状态
  const setSidebarHidden = (hidden) => {
    sidebarHidden.value = hidden
    localStorage.setItem('sidebarHidden', JSON.stringify(hidden))
  }

  // 初始化主题
  const initTheme = () => {
    const html = document.documentElement
    if (theme.value === 'dark') {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }
  }

  // 更新布局设置
  const updateLayoutSettings = (settings) => {
    layoutSettings.value = { ...layoutSettings.value, ...settings }
    localStorage.setItem('layoutSettings', JSON.stringify(layoutSettings.value))
  }

  return {
    // 状态
    theme,
    isDark,
    locale,
    sidebarCollapsed,
    sidebarHidden,
    layoutSettings,

    // 方法
    setTheme,
    toggleTheme,
    setLocale,
    toggleSidebar,
    setSidebarCollapsed,
    setSidebarHidden,
    initTheme,
    updateLayoutSettings
  }
})
