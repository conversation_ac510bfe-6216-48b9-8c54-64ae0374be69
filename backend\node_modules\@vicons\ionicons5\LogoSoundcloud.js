'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'LogoSoundcloud',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _cache[0] || (_cache[0] = [(0, vue_1.createStaticVNode)('<path d="M5.8 278a2.11 2.11 0 0 0-2 2L0 308.64l3.74 28.16a2.12 2.12 0 0 0 2.05 2a2.14 2.14 0 0 0 2-2l4.44-28.17L7.83 280a2.14 2.14 0 0 0-2-2z" fill="currentColor"></path><path d="M26.85 262.32a2.13 2.13 0 0 0-4.26 0l-5 46.32l5 45.3a2.13 2.13 0 0 0 4.26 0l5.73-45.31l-5.73-46.32z" fill="currentColor"></path><path d="M106.17 219.59a4 4 0 0 0-3.87 3.87l-4 85.22l4 55.08a3.88 3.88 0 0 0 7.75 0l4.53-55.08l-4.53-85.22a4 4 0 0 0-3.88-3.87z" fill="currentColor"></path><path d="M65.12 249.21a3.09 3.09 0 0 0-3 3l-4.52 56.45l4.51 54.63a3 3 0 0 0 6 0l5.13-54.63l-5.13-56.48a3.1 3.1 0 0 0-2.99-2.97z" fill="currentColor"></path><path d="M147.88 367.6a4.83 4.83 0 0 0 4.75-4.74l3.93-54.15l-3.93-113.46a4.75 4.75 0 0 0-9.5 0l-3.49 113.45l3.49 54.17a4.81 4.81 0 0 0 4.75 4.73z" fill="currentColor"></path><path d="M233.28 367.85a6.6 6.6 0 0 0 6.5-6.52l2.74-52.6l-2.74-131a6.5 6.5 0 1 0-13 0l-2.45 131c0 .08 2.45 52.67 2.45 52.67a6.59 6.59 0 0 0 6.5 6.45z" fill="currentColor"></path><path d="M190.26 367.65a5.67 5.67 0 0 0 5.62-5.64l3.34-53.33l-3.34-114.28a5.63 5.63 0 1 0-11.25 0l-3 114.29l3 53.32a5.66 5.66 0 0 0 5.63 5.6z" fill="currentColor"></path><path d="M85.56 367.15a3.53 3.53 0 0 0 3.44-3.41l4.83-55.09l-4.83-52.4a3.44 3.44 0 0 0-6.88 0l-4.26 52.38l4.26 55.08a3.5 3.5 0 0 0 3.44 3.44z" fill="currentColor"></path><path d="M44.84 364.13a2.67 2.67 0 0 0 2.57-2.52l5.43-53l-5.42-55a2.57 2.57 0 0 0-5.14 0l-4.78 55l4.78 53a2.62 2.62 0 0 0 2.56 2.53z" fill="currentColor"></path><path d="M211.69 192.53a6.1 6.1 0 0 0-6.07 6.09l-2.71 110.11l2.71 53a6.07 6.07 0 0 0 12.13 0l3-53l-3-110.13a6.1 6.1 0 0 0-6.06-6.07z" fill="currentColor"></path><path d="M127 367.71a4.41 4.41 0 0 0 4.31-4.3l4.23-54.71l-4.28-104.7a4.32 4.32 0 0 0-8.63 0l-3.74 104.7l3.75 54.73a4.38 4.38 0 0 0 4.36 4.28z" fill="currentColor"></path><path d="M174.17 362.54l3.63-53.8l-3.63-117.28a5.19 5.19 0 1 0-10.37 0l-3.23 117.28l3.23 53.83a5.18 5.18 0 0 0 10.36 0z" fill="currentColor"></path><path d="M449 241.1a62.42 62.42 0 0 0-24.33 4.9c-5-57.18-52.61-102-110.66-102a111.92 111.92 0 0 0-40.28 7.58c-4.75 1.85-6 3.76-6.06 7.46V360.4a7.66 7.66 0 0 0 6.8 7.5l174.56.11c34.78 0 63-28.41 63-63.45s-28.2-63.46-63-63.46z" fill="currentColor"></path><path d="M254.79 158.87a7 7 0 0 0-6.94 7L245 308.75l2.85 51.87a6.94 6.94 0 1 0 13.87-.06v.06l3.09-51.87l-3.09-142.93a7 7 0 0 0-6.93-6.95z" fill="currentColor"></path>', 14)]))
  }
})
