<template>
  <div class="login-container">
    <!-- 左侧插图区域 -->
    <div class="login-left">
      <div class="illustration">
        <div class="logo-section">
          <div class="logo">
            <Icon icon="mdi:triangle" class="logo-icon" />
            <span class="logo-text">Vben Admin Naive</span>
          </div>
        </div>

        <div class="illustration-image">
          <!-- 3D插图 -->
          <div class="isometric-illustration">
            <div class="platform">
              <div class="cube main-cube">
                <div class="cube-face front"></div>
                <div class="cube-face back"></div>
                <div class="cube-face right"></div>
                <div class="cube-face left"></div>
                <div class="cube-face top"></div>
                <div class="cube-face bottom"></div>
              </div>
              <div class="floating-elements">
                <div class="element element-1"></div>
                <div class="element element-2"></div>
                <div class="element element-3"></div>
              </div>
            </div>
          </div>
        </div>

        <div class="description">
          <h2>开箱即用的大型中后台管理系统</h2>
          <p>工程化、高性能、跨平台的中后台解决方案</p>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单 -->
    <div class="login-right">
      <div class="login-form-container">
        <div class="welcome-section">
          <h1 class="welcome-title">欢迎回来 👋</h1>
          <p class="welcome-subtitle">请输入您的账号和密码进行登录</p>
        </div>

        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          size="large"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="vben"
              clearable
            >
              <template #prefix>
                <Icon icon="mdi:account-outline" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="••••••"
              show-password
              clearable
            >
              <template #prefix>
                <Icon icon="mdi:lock-outline" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <div class="form-options">
              <el-checkbox v-model="loginForm.remember">记住密码</el-checkbox>
              <el-link type="primary" :underline="false">忘记密码?</el-link>
            </div>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-btn"
              :loading="loading"
              @click="handleLogin"
            >
              登录
            </el-button>
          </el-form-item>
        </el-form>

        <div class="login-footer">
          <div class="footer-links">
            <span>手机号登录</span>
            <span>二维码登录</span>
            <span>注册账号</span>
          </div>
        </div>
      </div>

      <div class="copyright">
        Copyright © 2024 Vben
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Icon } from '@iconify/vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loginFormRef = ref()
const loading = ref(false)

const loginForm = reactive({
  username: 'vben',
  password: '123456',
  remember: false
})

const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
}

const handleLogin = async () => {
  try {
    const valid = await loginFormRef.value.validate()
    if (!valid) return

    loading.value = true

    await authStore.login(loginForm)

    ElMessage.success('登录成功')
    router.push('/dashboard')
  } catch (error) {
    ElMessage.error(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
}

/* 左侧插图区域 */
.login-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: linear-gradient(135deg, #e8f4fd 0%, #f0e6f7 100%);
}

.illustration {
  max-width: 500px;
  text-align: center;
}

.logo-section {
  margin-bottom: 40px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 20px;
}

.logo-icon {
  font-size: 24px;
  color: #1976d2;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 3D插图 */
.illustration-image {
  margin-bottom: 40px;
  perspective: 1000px;
}

.isometric-illustration {
  position: relative;
  width: 300px;
  height: 300px;
  margin: 0 auto;
}

.platform {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  animation: float 6s ease-in-out infinite;
}

.main-cube {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120px;
  height: 120px;
  transform: translate(-50%, -50%) rotateX(-15deg) rotateY(25deg);
  transform-style: preserve-3d;
}

.cube-face {
  position: absolute;
  width: 120px;
  height: 120px;
  border: 2px solid #1976d2;
  opacity: 0.8;
}

.cube-face.front {
  background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
  transform: translateZ(60px);
}

.cube-face.back {
  background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
  transform: translateZ(-60px) rotateY(180deg);
}

.cube-face.right {
  background: linear-gradient(135deg, #1e88e5 0%, #1565c0 100%);
  transform: rotateY(90deg) translateZ(60px);
}

.cube-face.left {
  background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
  transform: rotateY(-90deg) translateZ(60px);
}

.cube-face.top {
  background: linear-gradient(135deg, #64b5f6 0%, #1976d2 100%);
  transform: rotateX(90deg) translateZ(60px);
}

.cube-face.bottom {
  background: linear-gradient(135deg, #0d47a1 0%, #01579b 100%);
  transform: rotateX(-90deg) translateZ(60px);
}

.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.element {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4fc3f7 0%, #29b6f6 100%);
  animation: orbit 8s linear infinite;
}

.element-1 {
  top: 20%;
  left: 20%;
  animation-delay: 0s;
}

.element-2 {
  top: 30%;
  right: 20%;
  animation-delay: -2s;
}

.element-3 {
  bottom: 20%;
  left: 30%;
  animation-delay: -4s;
}

.description h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.description p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.6;
}

/* 右侧登录表单 */
.login-right {
  width: 480px;
  display: flex;
  flex-direction: column;
  background: white;
  position: relative;
}

.login-form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40px;
}

.welcome-section {
  margin-bottom: 32px;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.login-form {
  margin-bottom: 24px;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.login-btn {
  width: 100%;
  height: 44px;
  font-size: 16px;
  background: #1976d2;
  border-color: #1976d2;
}

.login-btn:hover {
  background: #1565c0;
  border-color: #1565c0;
}

.login-footer {
  text-align: center;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 24px;
}

.footer-links span {
  font-size: 14px;
  color: #1976d2;
  cursor: pointer;
  transition: color 0.3s;
}

.footer-links span:hover {
  color: #1565c0;
}

.copyright {
  padding: 20px 40px;
  text-align: center;
  font-size: 12px;
  color: #999;
  border-top: 1px solid #f0f0f0;
}

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes orbit {
  0% {
    transform: rotate(0deg) translateX(50px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(50px) rotate(-360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
  }

  .login-left {
    display: none;
  }

  .login-right {
    width: 100%;
  }

  .login-form-container {
    padding: 20px;
  }
}
</style>
