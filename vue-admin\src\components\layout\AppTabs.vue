<template>
  <div class="app-tabs" v-if="tabsStore.tabs.length > 0">
    <div class="tabs-container">
      <div class="tabs-wrapper">
        <div 
          v-for="tab in tabsStore.tabs" 
          :key="tab.path"
          class="tab-item"
          :class="{ 'active': tab.path === route.path }"
          @click="handleTabClick(tab)"
        >
          <Icon :icon="tab.icon || 'mdi:file-document-outline'" class="tab-icon" />
          <span class="tab-title">{{ tab.title }}</span>
          <el-icon 
            v-if="tabsStore.tabs.length > 1"
            class="tab-close" 
            @click.stop="handleTabClose(tab)"
          >
            <Icon icon="mdi:close" />
          </el-icon>
        </div>
      </div>
      
      <!-- 右侧操作按钮 -->
      <div class="tabs-actions">
        <el-dropdown @command="handleTabAction">
          <el-button text size="small">
            <el-icon><Icon icon="mdi:chevron-down" /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="closeOthers">关闭其他</el-dropdown-item>
              <el-dropdown-item command="closeAll">关闭所有</el-dropdown-item>
              <el-dropdown-item command="refresh">刷新当前</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useTabsStore } from '@/stores/tabs'

const route = useRoute()
const router = useRouter()
const tabsStore = useTabsStore()

// 点击标签页
const handleTabClick = (tab) => {
  if (tab.path !== route.path) {
    router.push(tab.path)
  }
}

// 关闭标签页
const handleTabClose = (tab) => {
  tabsStore.removeTab(tab.path)
  
  // 如果关闭的是当前页面，需要跳转到其他页面
  if (tab.path === route.path && tabsStore.tabs.length > 0) {
    const lastTab = tabsStore.tabs[tabsStore.tabs.length - 1]
    router.push(lastTab.path)
  }
}

// 标签页操作
const handleTabAction = (command) => {
  switch (command) {
    case 'closeOthers':
      tabsStore.closeOthers(route.path)
      break
    case 'closeAll':
      tabsStore.closeAll()
      router.push('/dashboard')
      break
    case 'refresh':
      window.location.reload()
      break
  }
}
</script>

<style scoped>
.app-tabs {
  background: var(--el-bg-color-page);
  border-bottom: 1px solid var(--el-border-color-light);
  height: 40px;
  display: flex;
  align-items: flex-end;
  padding: 0 8px;
}

.tabs-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
}

.tabs-wrapper {
  display: flex;
  align-items: center;
  flex: 1;
  overflow-x: auto;
  height: 100%;
}

.tabs-wrapper::-webkit-scrollbar {
  height: 0;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 0 12px;
  height: 32px;
  margin: 4px 1px;
  background: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-size: 12px;
  color: var(--el-text-color-regular);
  min-width: 100px;
  max-width: 180px;
  position: relative;
}

.tab-item:hover {
  background: var(--el-color-primary-light-9);
  border-color: var(--el-color-primary-light-7);
  color: var(--el-color-primary);
}

.tab-item.active {
  background: white;
  border-color: var(--el-border-color-light);
  color: var(--el-color-primary);
  z-index: 1;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 1px;
  background: white;
}

.tab-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tab-close {
  font-size: 12px;
  padding: 2px;
  border-radius: 2px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.tab-close:hover {
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

.tab-item.active .tab-close:hover {
  background: var(--el-color-danger-light-9);
  color: var(--el-color-danger);
}

.tabs-actions {
  padding: 0 8px;
  border-left: 1px solid var(--el-border-color-light);
  height: 100%;
  display: flex;
  align-items: center;
}

/* 深色模式适配 */
.dark .app-tabs {
  background: var(--el-bg-color);
  border-bottom-color: var(--el-border-color);
}

.dark .tab-item {
  background: var(--el-bg-color-page);
  border-color: var(--el-border-color);
  color: var(--el-text-color-regular);
}

.dark .tab-item:hover {
  background: var(--el-color-primary-light-3);
  border-color: var(--el-color-primary-light-5);
}

.dark .tabs-actions {
  border-left-color: var(--el-border-color);
}
</style>
