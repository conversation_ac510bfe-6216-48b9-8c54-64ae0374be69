<template>
  <div class="app-tabs" v-if="tabsStore.tabs.length > 0">
    <div class="tabs-container">
      <div class="tabs-wrapper">
        <div 
          v-for="tab in tabsStore.tabs" 
          :key="tab.path"
          class="tab-item"
          :class="{ 'active': tab.path === route.path }"
          @click="handleTabClick(tab)"
        >
          <Icon :icon="tab.icon || 'mdi:file-document-outline'" class="tab-icon" />
          <span class="tab-title">{{ tab.title }}</span>
          <el-icon 
            v-if="tabsStore.tabs.length > 1"
            class="tab-close" 
            @click.stop="handleTabClose(tab)"
          >
            <Icon icon="mdi:close" />
          </el-icon>
        </div>
      </div>
      
      <!-- 右侧操作按钮 -->
      <div class="tabs-actions">
        <el-dropdown @command="handleTabAction">
          <el-button text size="small">
            <el-icon><Icon icon="mdi:chevron-down" /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="closeOthers">关闭其他</el-dropdown-item>
              <el-dropdown-item command="closeAll">关闭所有</el-dropdown-item>
              <el-dropdown-item command="refresh">刷新当前</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { Icon } from '@iconify/vue'
import { useTabsStore } from '@/stores/tabs'

const route = useRoute()
const router = useRouter()
const tabsStore = useTabsStore()

// 点击标签页
const handleTabClick = (tab) => {
  if (tab.path !== route.path) {
    router.push(tab.path)
  }
}

// 关闭标签页
const handleTabClose = (tab) => {
  tabsStore.removeTab(tab.path)
  
  // 如果关闭的是当前页面，需要跳转到其他页面
  if (tab.path === route.path && tabsStore.tabs.length > 0) {
    const lastTab = tabsStore.tabs[tabsStore.tabs.length - 1]
    router.push(lastTab.path)
  }
}

// 标签页操作
const handleTabAction = (command) => {
  switch (command) {
    case 'closeOthers':
      tabsStore.closeOthers(route.path)
      break
    case 'closeAll':
      tabsStore.closeAll()
      router.push('/dashboard')
      break
    case 'refresh':
      window.location.reload()
      break
  }
}
</script>

<style scoped>
.app-tabs {
  background: white;
  border-bottom: 1px solid var(--el-border-color-light);
  height: 38px;
  display: flex;
  align-items: flex-end;
  padding: 0 12px 0 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.tabs-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
}

.tabs-wrapper {
  display: flex;
  align-items: flex-end;
  flex: 1;
  overflow-x: auto;
  height: 100%;
  margin-top: 3px;
}

.tabs-wrapper::-webkit-scrollbar {
  height: 0;
}

.tab-item {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  margin: 0 1px;
  background: transparent;
  border: none;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  font-size: 13px;
  color: var(--el-text-color-regular);
  min-width: 80px;
  max-width: 160px;
  position: relative;
  height: 36px;
  box-sizing: border-box;
}

.tab-item:hover {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.tab-item.active {
  background: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  font-weight: 500;
  border-radius: 8px 8px 0 0;
  position: relative;
  z-index: 1;
}

/* 激活标签的底部延伸 */
.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: -8px;
  right: -8px;
  height: 1px;
  background: var(--el-color-primary-light-9);
  z-index: 2;
}

/* 左下角圆角效果 */
.tab-item.active::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: -8px;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle at 0 0, transparent 8px, var(--el-color-primary-light-9) 8px);
  z-index: 1;
}

/* 右下角圆角效果 */
.tab-item.active {
  overflow: visible;
}

/* 使用 box-shadow 创建右下角圆角的背景延伸 */
.tab-item.active {
  box-shadow: 8px 0 0 -8px var(--el-color-primary-light-9);
}

/* 右下角圆角的遮罩效果 */
.tabs-wrapper {
  position: relative;
}

.tab-item.active + * {
  position: relative;
}

.tab-item.active + *::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: -8px;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle at 8px 0, transparent 8px, white 8px);
  z-index: 3;
}

.tab-icon {
  font-size: 14px;
  flex-shrink: 0;
  opacity: 0.8;
}

.tab-item.active .tab-icon {
  opacity: 1;
}

.tab-title {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1;
}

.tab-close {
  font-size: 12px;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s ease;
  flex-shrink: 0;
  opacity: 0.6;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-item:hover .tab-close {
  opacity: 1;
}

.tab-close:hover {
  background: var(--el-color-danger-light-8);
  color: var(--el-color-danger);
  opacity: 1;
}

.tabs-actions {
  padding: 0 8px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  height: 36px;
}

/* 深色模式适配 */
.dark .app-tabs {
  background: var(--el-bg-color);
  border-bottom-color: var(--el-border-color);
}

.dark .tab-item {
  color: var(--el-text-color-regular);
}

.dark .tab-item:hover {
  background: var(--el-color-primary-dark-2);
  color: var(--el-color-primary-light-3);
}

.dark .tab-item.active {
  background: var(--el-color-primary-dark-2);
  color: var(--el-color-primary-light-3);
}
</style>
