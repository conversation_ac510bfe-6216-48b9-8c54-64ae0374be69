import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/components/layout/Layout.vue'),
    meta: { requiresAuth: true },
    redirect: '/dashboard',
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        meta: { 
          title: 'dashboard',
          icon: 'home-outline',
          requiresAuth: true 
        }
      },
      {
        path: '/system',
        name: 'System',
        meta: { 
          title: 'system',
          icon: 'settings-outline',
          requiresAuth: true 
        },
        children: [
          {
            path: '/system/users',
            name: 'Users',
            component: () => import('@/views/system/Users.vue'),
            meta: { 
              title: 'users',
              icon: 'people-outline',
              requiresAuth: true 
            }
          },
          {
            path: '/system/roles',
            name: 'Roles',
            component: () => import('@/views/system/Roles.vue'),
            meta: { 
              title: 'roles',
              icon: 'person-circle-outline',
              requiresAuth: true 
            }
          },
          {
            path: '/system/permissions',
            name: 'Permissions',
            component: () => import('@/views/system/Permissions.vue'),
            meta: { 
              title: 'permissions',
              icon: 'key-outline',
              requiresAuth: true 
            }
          }
        ]
      },
      {
        path: '/logs',
        name: 'Logs',
        component: () => import('@/views/logs/Logs.vue'),
        meta: { 
          title: 'logs',
          icon: 'document-text-outline',
          requiresAuth: true 
        }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/dashboard')
  } else {
    next()
  }
})

export default router
