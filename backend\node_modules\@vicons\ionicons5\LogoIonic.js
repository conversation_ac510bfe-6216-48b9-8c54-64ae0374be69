'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'LogoIonic',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M256 153.9A102.1 102.1 0 1 0 358.1 256A102.23 102.23 0 0 0 256 153.9z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            (0, vue_1.createElementVNode)(
              'circle',
              {
                cx: '402.59',
                cy: '116.45',
                r: '46.52',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            ),
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M459.86 163.2l-1.95-4.28l-3.11 3.52a70 70 0 0 1-28.06 19.32l-3 1.1l1.22 2.93A181.43 181.43 0 0 1 439 256c0 100.92-82.1 183-183 183S73 356.92 73 256S155.08 73 256 73a180.94 180.94 0 0 1 78.43 17.7l2.87 1.3l1.25-2.92A70.19 70.19 0 0 1 359.21 62l3.67-2.93l-4.17-2.07A221.61 221.61 0 0 0 256 32C132.49 32 32 132.49 32 256s100.49 224 224 224s224-100.49 224-224a222.19 222.19 0 0 0-20.14-92.8z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
