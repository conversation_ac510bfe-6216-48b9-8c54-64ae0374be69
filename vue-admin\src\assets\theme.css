/* 自定义主题色系 - 绿色主题 */
:root {
  /* 主色系 - 绿色 */
  --el-color-primary: #10b981;
  --el-color-primary-light-1: #34d399;
  --el-color-primary-light-2: #6ee7b7;
  --el-color-primary-light-3: #a7f3d0;
  --el-color-primary-light-4: #d1fae5;
  --el-color-primary-light-5: #ecfdf5;
  --el-color-primary-light-6: #f0fdf4;
  --el-color-primary-light-7: #f7fee7;
  --el-color-primary-light-8: #f9fef9;
  --el-color-primary-light-9: #fafffe;
  
  --el-color-primary-dark-1: #059669;
  --el-color-primary-dark-2: #047857;
  --el-color-primary-dark-3: #065f46;
  --el-color-primary-dark-4: #064e3b;
  --el-color-primary-dark-5: #022c22;
}

/* 深色模式下的主色系调整 */
.dark {
  --el-color-primary: #34d399;
  --el-color-primary-light-1: #6ee7b7;
  --el-color-primary-light-2: #a7f3d0;
  --el-color-primary-light-3: #d1fae5;
  --el-color-primary-light-4: #ecfdf5;
  --el-color-primary-light-5: #f0fdf4;
  --el-color-primary-light-6: #f7fee7;
  --el-color-primary-light-7: #f9fef9;
  --el-color-primary-light-8: #fafffe;
  --el-color-primary-light-9: #ffffff;
  
  --el-color-primary-dark-1: #10b981;
  --el-color-primary-dark-2: #059669;
  --el-color-primary-dark-3: #047857;
  --el-color-primary-dark-4: #065f46;
  --el-color-primary-dark-5: #064e3b;
}

/* 成功色系 - 保持绿色但调整色调 */
:root {
  --el-color-success: #22c55e;
  --el-color-success-light-1: #4ade80;
  --el-color-success-light-2: #86efac;
  --el-color-success-light-3: #bbf7d0;
  --el-color-success-light-4: #dcfce7;
  --el-color-success-light-5: #f0fdf4;
  --el-color-success-light-6: #f7fee7;
  --el-color-success-light-7: #f9fef9;
  --el-color-success-light-8: #fafffe;
  --el-color-success-light-9: #ffffff;
  
  --el-color-success-dark-1: #16a34a;
  --el-color-success-dark-2: #15803d;
  --el-color-success-dark-3: #166534;
  --el-color-success-dark-4: #14532d;
  --el-color-success-dark-5: #052e16;
}

/* 信息色系 - 使用青色 */
:root {
  --el-color-info: #06b6d4;
  --el-color-info-light-1: #22d3ee;
  --el-color-info-light-2: #67e8f9;
  --el-color-info-light-3: #a5f3fc;
  --el-color-info-light-4: #cffafe;
  --el-color-info-light-5: #ecfeff;
  --el-color-info-light-6: #f0fdff;
  --el-color-info-light-7: #f7feff;
  --el-color-info-light-8: #fafffe;
  --el-color-info-light-9: #ffffff;
  
  --el-color-info-dark-1: #0891b2;
  --el-color-info-dark-2: #0e7490;
  --el-color-info-dark-3: #155e75;
  --el-color-info-dark-4: #164e63;
  --el-color-info-dark-5: #083344;
}

/* 警告色系 - 使用橙色 */
:root {
  --el-color-warning: #f59e0b;
  --el-color-warning-light-1: #fbbf24;
  --el-color-warning-light-2: #fcd34d;
  --el-color-warning-light-3: #fde68a;
  --el-color-warning-light-4: #fef3c7;
  --el-color-warning-light-5: #fffbeb;
  --el-color-warning-light-6: #fffcf0;
  --el-color-warning-light-7: #fffef7;
  --el-color-warning-light-8: #fffffe;
  --el-color-warning-light-9: #ffffff;
  
  --el-color-warning-dark-1: #d97706;
  --el-color-warning-dark-2: #b45309;
  --el-color-warning-dark-3: #92400e;
  --el-color-warning-dark-4: #78350f;
  --el-color-warning-dark-5: #451a03;
}

/* 危险色系 - 保持红色 */
:root {
  --el-color-danger: #ef4444;
  --el-color-danger-light-1: #f87171;
  --el-color-danger-light-2: #fca5a5;
  --el-color-danger-light-3: #fecaca;
  --el-color-danger-light-4: #fee2e2;
  --el-color-danger-light-5: #fef2f2;
  --el-color-danger-light-6: #fef7f7;
  --el-color-danger-light-7: #fffbfb;
  --el-color-danger-light-8: #fffffe;
  --el-color-danger-light-9: #ffffff;
  
  --el-color-danger-dark-1: #dc2626;
  --el-color-danger-dark-2: #b91c1c;
  --el-color-danger-dark-3: #991b1b;
  --el-color-danger-dark-4: #7f1d1d;
  --el-color-danger-dark-5: #450a0a;
}
