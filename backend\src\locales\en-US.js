export default {
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    reset: 'Reset',
    submit: 'Submit',
    back: 'Back',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info'
  },
  menu: {
    dashboard: 'Dashboard',
    system: 'System',
    users: 'Users',
    roles: 'Roles',
    permissions: 'Permissions',
    logs: 'Logs'
  },
  login: {
    title: 'User Login',
    username: '<PERSON>rna<PERSON>',
    password: 'Password',
    login: 'Login',
    logout: 'Logout',
    remember: 'Remember Password',
    forgot: 'Forgot Password?'
  },
  user: {
    username: 'Userna<PERSON>',
    email: 'Email',
    phone: 'Phone',
    status: 'Status',
    role: 'Role',
    createTime: 'Create Time',
    updateTime: 'Update Time',
    active: 'Active',
    inactive: 'Inactive'
  },
  role: {
    name: 'Role Name',
    code: 'Role Code',
    description: 'Description',
    permissions: 'Permissions',
    createTime: 'Create Time',
    updateTime: 'Update Time'
  },
  permission: {
    name: 'Permission Name',
    code: 'Permission Code',
    type: 'Permission Type',
    resource: 'Resource',
    action: 'Action',
    description: 'Description'
  },
  log: {
    user: 'User',
    action: 'Action',
    resource: 'Resource',
    ip: 'IP Address',
    userAgent: 'User Agent',
    createTime: 'Create Time',
    description: 'Description'
  }
}
