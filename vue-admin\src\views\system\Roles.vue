<template>
  <div class="roles-page">
    <div class="page-header">
      <h1>{{ $t('menu.roles') }}</h1>
      <el-button type="primary">
        <el-icon><Plus /></el-icon>
        添加角色
      </el-button>
    </div>

    <el-card>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="角色名称" />
        <el-table-column prop="code" label="角色编码" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="userCount" label="用户数量" width="100" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" size="small" text>
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="danger" size="small" text>
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Plus, Edit, Delete } from '@element-plus/icons-vue'

const { t } = useI18n()

const tableData = ref([])

const loadRoles = () => {
  tableData.value = [
    {
      id: 1,
      name: '超级管理员',
      code: 'super_admin',
      description: '拥有所有权限的超级管理员',
      userCount: 1,
      createTime: '2024-01-01 10:00:00'
    },
    {
      id: 2,
      name: '普通用户',
      code: 'user',
      description: '普通用户角色',
      userCount: 10,
      createTime: '2024-01-02 10:00:00'
    }
  ]
}

onMounted(() => {
  loadRoles()
})
</script>

<style scoped>
.roles-page {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}
</style>
