<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        {{ $t('menu.dashboard') }}
      </h1>
    </div>
    
    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <n-card class="hover:shadow-lg transition-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
            <n-icon size="24" class="text-blue-600 dark:text-blue-400">
              <people-outline />
            </n-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              {{ $t('user.total', '总用户数') }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.totalUsers }}
            </p>
          </div>
        </div>
      </n-card>
      
      <n-card class="hover:shadow-lg transition-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
            <n-icon size="24" class="text-green-600 dark:text-green-400">
              <person-circle-outline />
            </n-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              {{ $t('role.total', '总角色数') }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.totalRoles }}
            </p>
          </div>
        </div>
      </n-card>
      
      <n-card class="hover:shadow-lg transition-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
            <n-icon size="24" class="text-yellow-600 dark:text-yellow-400">
              <key-outline />
            </n-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              {{ $t('permission.total', '总权限数') }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.totalPermissions }}
            </p>
          </div>
        </div>
      </n-card>
      
      <n-card class="hover:shadow-lg transition-shadow">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
            <n-icon size="24" class="text-purple-600 dark:text-purple-400">
              <document-text-outline />
            </n-icon>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">
              {{ $t('log.total', '今日日志') }}
            </p>
            <p class="text-2xl font-bold text-gray-900 dark:text-white">
              {{ stats.todayLogs }}
            </p>
          </div>
        </div>
      </n-card>
    </div>
    
    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <n-card title="用户增长趋势">
        <div class="h-64 flex items-center justify-center text-gray-500">
          图表组件待实现
        </div>
      </n-card>
      
      <n-card title="系统活动">
        <div class="h-64 flex items-center justify-center text-gray-500">
          活动日志组件待实现
        </div>
      </n-card>
    </div>
    
    <!-- 快速操作 -->
    <n-card title="快速操作">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <n-button type="primary" @click="$router.push('/system/users')">
          <template #icon>
            <n-icon><people-outline /></n-icon>
          </template>
          用户管理
        </n-button>
        
        <n-button type="info" @click="$router.push('/system/roles')">
          <template #icon>
            <n-icon><person-circle-outline /></n-icon>
          </template>
          角色管理
        </n-button>
        
        <n-button type="warning" @click="$router.push('/system/permissions')">
          <template #icon>
            <n-icon><key-outline /></n-icon>
          </template>
          权限管理
        </n-button>
        
        <n-button type="success" @click="$router.push('/logs')">
          <template #icon>
            <n-icon><document-text-outline /></n-icon>
          </template>
          日志查看
        </n-button>
      </div>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { NCard, NButton, NIcon } from 'naive-ui'
import {
  PeopleOutline,
  PersonCircleOutline,
  KeyOutline,
  DocumentTextOutline
} from '@vicons/ionicons5'

const { t } = useI18n()

const stats = ref({
  totalUsers: 0,
  totalRoles: 0,
  totalPermissions: 0,
  todayLogs: 0
})

const loadStats = async () => {
  // 模拟数据，实际应该从API获取
  stats.value = {
    totalUsers: 156,
    totalRoles: 8,
    totalPermissions: 24,
    todayLogs: 89
  }
}

onMounted(() => {
  loadStats()
})
</script>
