'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'LogoSkype',
  render: function render(_ctx, _cache) {
    return (
      (0, vue_1.openBlock)(),
      (0, vue_1.createElementBlock)(
        'svg',
        _hoisted_1,
        _cache[0] ||
          (_cache[0] = [
            (0, vue_1.createElementVNode)(
              'path',
              {
                d: 'M467.16 303.6a205.69 205.69 0 0 0 4.9-45.15c0-116.32-95.69-210.7-213.79-210.7a221.83 221.83 0 0 0-36.52 3A123.58 123.58 0 0 0 155.93 32C87.55 32 32 86.72 32 154.15A119.56 119.56 0 0 0 49 216a211.16 211.16 0 0 0-4.32 42.35c0 116.44 95.69 210.7 213.67 210.7a214 214 0 0 0 39.09-3.5A125.45 125.45 0 0 0 356.07 480C424.57 480 480 425.28 480 357.85a118 118 0 0 0-12.84-54.25zM368 359c-9.92 13.76-24.51 24.73-43.41 32.43S283.36 403 257.69 403c-30.69 0-56.36-5.37-76.55-15.87a101 101 0 0 1-35.24-30.8c-9.11-12.83-13.66-25.66-13.66-38c0-7.7 3-14.35 8.87-19.95c5.84-5.37 13.42-8.17 22.29-8.17c7.35 0 13.65 2.1 18.79 6.42c4.9 4.08 9.1 10.15 12.48 18.08A108.09 108.09 0 0 0 207 336.15q6.32 8.22 17.86 13.65c7.82 3.62 18.2 5.48 31 5.48c17.62 0 32.09-3.73 42.94-11.08c10.74-7.12 15.88-15.75 15.88-26.25c0-8.28-2.69-14.82-8.29-19.95c-5.83-5.37-13.42-9.57-22.87-12.37c-9.69-3-22.87-6.18-39.21-9.56c-22.17-4.67-41-10.27-56-16.57c-15.28-6.42-27.65-15.4-36.76-26.48c-9.22-11.32-13.77-25.55-13.77-42.24a67.86 67.86 0 0 1 14.47-42.58c9.57-12.25 23.46-21.82 41.55-28.35c17.74-6.53 38.86-9.8 62.66-9.8c19.14 0 35.83 2.22 49.83 6.42s25.91 10.15 35.36 17.38s16.34 14.93 20.77 23s6.66 16.22 6.66 24c0 7.46-2.92 14.35-8.76 20.3a29.65 29.65 0 0 1-21.94 9.1c-7.93 0-14.12-1.87-18.43-5.6c-4-3.5-8.17-8.87-12.72-16.69c-5.37-9.91-11.79-17.85-19.14-23.45c-7.24-5.36-19.14-8.16-35.71-8.16c-15.29 0-27.77 3-37 9c-8.87 5.72-13.19 12.37-13.19 20.18a18.26 18.26 0 0 0 4.32 12.25a38.13 38.13 0 0 0 12.72 9.57a90.14 90.14 0 0 0 17.15 6.53c6 1.64 15.87 4.09 29.53 7.12c17.38 3.62 33.25 7.82 47.26 12.13c14.24 4.55 26.49 10 36.52 16.45a72.93 72.93 0 0 1 24.16 25.09c5.72 10 8.64 22.63 8.64 37.1A75.09 75.09 0 0 1 368 359z',
                fill: 'currentColor'
              },
              null,
              -1 /* HOISTED */
            )
          ])
      )
    )
  }
})
