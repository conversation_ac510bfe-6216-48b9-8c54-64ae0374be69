<template>
  <div class="dashboard">
    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card user-card" shadow="never">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-label">用户量</div>
              <div class="stats-value">{{ stats.userCount.toLocaleString() }}</div>
              <div class="stats-total">总用户量 {{ stats.totalUsers.toLocaleString() }}</div>
            </div>
            <div class="stats-icon">
              <Icon icon="mdi:account-group" />
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card visit-card" shadow="never">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-label">访问量</div>
              <div class="stats-value">{{ stats.visitCount.toLocaleString() }}</div>
              <div class="stats-total">总访问量 {{ stats.totalVisits.toLocaleString() }}</div>
            </div>
            <div class="stats-icon">
              <Icon icon="mdi:eye" />
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card download-card" shadow="never">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-label">下载量</div>
              <div class="stats-value">{{ stats.downloadCount.toLocaleString() }}</div>
              <div class="stats-total">总下载量 {{ stats.totalDownloads.toLocaleString() }}</div>
            </div>
            <div class="stats-icon">
              <Icon icon="mdi:download" />
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card transaction-card" shadow="never">
          <div class="stats-content">
            <div class="stats-info">
              <div class="stats-label">成交额</div>
              <div class="stats-value">{{ stats.transactionAmount.toLocaleString() }}</div>
              <div class="stats-total">总成交额 {{ stats.totalTransactions.toLocaleString() }}</div>
            </div>
            <div class="stats-icon">
              <Icon icon="mdi:currency-usd" />
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="16" class="chart-row">
      <el-col :span="24">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-header">
              <div class="chart-tabs">
                <span class="tab-item active">流量趋势</span>
                <span class="tab-item">访问量</span>
              </div>
            </div>
          </template>

          <div class="chart-container">
            <div class="chart-placeholder">
              <!-- 模拟图表区域 -->
              <svg viewBox="0 0 800 300" class="trend-chart">
                <!-- 背景网格 -->
                <defs>
                  <pattern id="grid" width="40" height="30" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 30" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />

                <!-- 面积图 -->
                <path d="M 0 250 Q 100 200 200 180 T 400 160 T 600 140 T 800 120 L 800 300 L 0 300 Z"
                      fill="url(#gradient1)" opacity="0.6"/>
                <path d="M 0 280 Q 100 240 200 220 T 400 200 T 600 180 T 800 160 L 800 300 L 0 300 Z"
                      fill="url(#gradient2)" opacity="0.8"/>

                <!-- 渐变定义 -->
                <defs>
                  <linearGradient id="gradient1" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#4facfe;stop-opacity:0" />
                  </linearGradient>
                  <linearGradient id="gradient2" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" style="stop-color:#00f2fe;stop-opacity:1" />
                    <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:0" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 底部图表 -->
    <el-row :gutter="16" class="bottom-charts">
      <el-col :xs="24" :sm="24" :md="8">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>访问数量</span>
          </template>
          <div class="mini-chart">
            <!-- 雷达图模拟 -->
            <div class="radar-chart">
              <svg viewBox="0 0 200 200" class="radar-svg">
                <!-- 雷达网格 -->
                <polygon points="100,20 173,65 173,135 100,180 27,135 27,65"
                         fill="none" stroke="#e0e0e0" stroke-width="1"/>
                <polygon points="100,40 153,75 153,125 100,160 47,125 47,75"
                         fill="none" stroke="#e0e0e0" stroke-width="1"/>
                <polygon points="100,60 133,85 133,115 100,140 67,115 67,85"
                         fill="none" stroke="#e0e0e0" stroke-width="1"/>

                <!-- 数据区域 -->
                <polygon points="100,30 160,70 150,130 100,150 50,120 60,70"
                         fill="#4facfe" opacity="0.3" stroke="#4facfe" stroke-width="2"/>
              </svg>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>访问来源</span>
          </template>
          <div class="mini-chart">
            <!-- 饼图模拟 -->
            <div class="pie-chart">
              <svg viewBox="0 0 200 200" class="pie-svg">
                <circle cx="100" cy="100" r="60" fill="#4facfe" stroke="white" stroke-width="2"/>
                <circle cx="100" cy="100" r="60" fill="#00f2fe" stroke="white" stroke-width="2"
                        stroke-dasharray="113 377" stroke-dashoffset="0" transform="rotate(-90 100 100)"/>
                <circle cx="100" cy="100" r="60" fill="#43e97b" stroke="white" stroke-width="2"
                        stroke-dasharray="75 377" stroke-dashoffset="-113" transform="rotate(-90 100 100)"/>
              </svg>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="8">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>访问来源</span>
          </template>
          <div class="mini-chart">
            <!-- 环形图模拟 -->
            <div class="donut-chart">
              <svg viewBox="0 0 200 200" class="donut-svg">
                <circle cx="100" cy="100" r="60" fill="none" stroke="#4facfe" stroke-width="20"/>
                <circle cx="100" cy="100" r="60" fill="none" stroke="#00f2fe" stroke-width="20"
                        stroke-dasharray="188 377" stroke-dashoffset="0" transform="rotate(-90 100 100)"/>
                <circle cx="100" cy="100" r="60" fill="none" stroke="#43e97b" stroke-width="20"
                        stroke-dasharray="94 377" stroke-dashoffset="-188" transform="rotate(-90 100 100)"/>
              </svg>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { Icon } from '@iconify/vue'

const { t } = useI18n()

// 统计数据
const stats = ref({
  userCount: 0,
  totalUsers: 0,
  visitCount: 0,
  totalVisits: 0,
  downloadCount: 0,
  totalDownloads: 0,
  transactionAmount: 0,
  totalTransactions: 0
})

// 加载统计数据
const loadStats = async () => {
  // 模拟API调用
  setTimeout(() => {
    stats.value = {
      userCount: 2000,
      totalUsers: 120000,
      visitCount: 20000,
      totalVisits: 500000,
      downloadCount: 8000,
      totalDownloads: 120000,
      transactionAmount: 5000,
      totalTransactions: 50000
    }
  }, 500)
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.stats-row {
  margin-bottom: 16px;
}

.stats-card {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.stats-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
}

.stats-info {
  flex: 1;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.stats-total {
  font-size: 12px;
  color: #999;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

/* 不同卡片的图标颜色 */
.user-card .stats-icon {
  background: #e3f2fd;
  color: #1976d2;
}

.visit-card .stats-icon {
  background: #fff3e0;
  color: #f57c00;
}

.download-card .stats-icon {
  background: #f3e5f5;
  color: #7b1fa2;
}

.transaction-card .stats-icon {
  background: #e8f5e8;
  color: #388e3c;
}

.chart-row {
  margin-bottom: 16px;
}

.chart-card {
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-tabs {
  display: flex;
  gap: 24px;
}

.tab-item {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  padding: 4px 0;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1976d2;
  border-bottom-color: #1976d2;
}

.chart-container {
  height: 300px;
  padding: 20px 0;
}

.chart-placeholder {
  width: 100%;
  height: 100%;
}

.trend-chart {
  width: 100%;
  height: 100%;
}

.bottom-charts {
  margin-bottom: 16px;
}

.mini-chart {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radar-chart,
.pie-chart,
.donut-chart {
  width: 150px;
  height: 150px;
}

.radar-svg,
.pie-svg,
.donut-svg {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-content {
    padding: 16px;
  }

  .stats-value {
    font-size: 20px;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .chart-container {
    height: 200px;
  }

  .mini-chart {
    height: 150px;
  }

  .radar-chart,
  .pie-chart,
  .donut-chart {
    width: 120px;
    height: 120px;
  }

  .chart-tabs {
    gap: 16px;
  }

  .tab-item {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .bottom-charts .el-col {
    margin-bottom: 16px;
  }
}
</style>
