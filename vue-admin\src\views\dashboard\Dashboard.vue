<template>
  <div class="dashboard">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">{{ $t('menu.dashboard') }}</h1>
      <p class="page-description">欢迎使用Vue Admin管理系统</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.totalUsers }}</div>
              <div class="stats-label">总用户数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon role-icon">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.totalRoles }}</div>
              <div class="stats-label">总角色数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon permission-icon">
              <el-icon><Key /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.totalPermissions }}</div>
              <div class="stats-label">总权限数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="12" :md="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-content">
            <div class="stats-icon log-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-value">{{ stats.todayLogs }}</div>
              <div class="stats-label">今日日志</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-card class="quick-actions" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>快速操作</span>
        </div>
      </template>

      <el-row :gutter="16">
        <el-col :xs="12" :sm="6" :md="3">
          <div class="action-item" @click="$router.push('/system/users')">
            <div class="action-icon user-action">
              <el-icon><User /></el-icon>
            </div>
            <div class="action-text">用户管理</div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="3">
          <div class="action-item" @click="$router.push('/system/roles')">
            <div class="action-icon role-action">
              <el-icon><UserFilled /></el-icon>
            </div>
            <div class="action-text">角色管理</div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="3">
          <div class="action-item" @click="$router.push('/system/permissions')">
            <div class="action-icon permission-action">
              <el-icon><Key /></el-icon>
            </div>
            <div class="action-text">权限管理</div>
          </div>
        </el-col>

        <el-col :xs="12" :sm="6" :md="3">
          <div class="action-item" @click="$router.push('/logs')">
            <div class="action-icon log-action">
              <el-icon><Document /></el-icon>
            </div>
            <div class="action-text">日志管理</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { User, UserFilled, Key, Document } from '@element-plus/icons-vue'

const { t } = useI18n()

// 统计数据
const stats = ref({
  totalUsers: 0,
  totalRoles: 0,
  totalPermissions: 0,
  todayLogs: 0
})

// 加载统计数据
const loadStats = async () => {
  // 模拟API调用
  setTimeout(() => {
    stats.value = {
      totalUsers: 1256,
      totalRoles: 8,
      totalPermissions: 24,
      todayLogs: 89
    }
  }, 500)
}

onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.page-description {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.stats-row {
  margin-bottom: 24px;
}

.stats-card {
  height: 120px;
  transition: transform 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-4px);
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 16px;
}

.user-icon { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.role-icon { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.permission-icon { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.log-icon { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 28px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.quick-actions {
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-item:hover {
  background: var(--el-bg-color-page);
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  margin-bottom: 8px;
}

.user-action { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.role-action { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.permission-action { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.log-action { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }

.action-text {
  font-size: 14px;
  color: var(--el-text-color-primary);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .stats-row {
    margin-bottom: 16px;
  }

  .action-item {
    padding: 16px 8px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .action-text {
    font-size: 12px;
  }
}
</style>
