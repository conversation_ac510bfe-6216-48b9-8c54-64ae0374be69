{"name": "backend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vicons/ionicons5": "^0.13.0", "axios": "^1.10.0", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-i18n": "^9.14.4", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@vitejs/plugin-vue": "^6.0.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^7.0.3"}}