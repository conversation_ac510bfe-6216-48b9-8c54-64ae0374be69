'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vue_1 = require('vue')
const _hoisted_1 = {
  xmlns: 'http://www.w3.org/2000/svg',
  'xmlns:xlink': 'http://www.w3.org/1999/xlink',
  viewBox: '0 0 512 512'
}
exports.default = (0, vue_1.defineComponent)({
  name: 'LogoWebComponent',
  render: function render(_ctx, _cache) {
    return (0, vue_1.openBlock)(), (0, vue_1.createElementBlock)('svg', _hoisted_1, _cache[0] || (_cache[0] = [(0, vue_1.createStaticVNode)('<path fill="none" d="M179.9 388l-76.16-132l76.16 132z"></path><path fill="none" d="M179.9 388h152.21l76.15-132l-76.15-132H179.9l-76.16 132l76.16 132z"></path><path fill="none" d="M103.74 256l76.16-132l-76.16 132z"></path><path d="M496 256L376 48H239.74l-43.84 76h136.21l76.15 132l-76.15 132H195.9l43.84 76H376l120-208z" fill="currentColor"></path><path d="M179.9 388l-76.16-132l76.16-132l43.84-76H136L16 256l120 208h87.74l-43.84-76z" fill="currentColor"></path>', 5)]))
  }
})
