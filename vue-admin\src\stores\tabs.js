import { defineStore } from 'pinia'

export const useTabsStore = defineStore('tabs', {
  state: () => ({
    tabs: []
  }),

  getters: {
    // 获取当前激活的标签页
    activeTab: (state) => (currentPath) => {
      return state.tabs.find(tab => tab.path === currentPath)
    }
  },

  actions: {
    // 添加标签页
    addTab(route) {
      // 检查是否已存在
      const existingTab = this.tabs.find(tab => tab.path === route.path)
      if (existingTab) {
        // 更新标题（可能会变化）
        existingTab.title = route.meta?.title || route.name || '未知页面'
        return
      }

      // 添加新标签页
      const newTab = {
        path: route.path,
        name: route.name,
        title: route.meta?.title || route.name || '未知页面',
        icon: route.meta?.icon || 'mdi:file-document-outline'
      }

      this.tabs.push(newTab)
    },

    // 移除标签页
    removeTab(path) {
      const index = this.tabs.findIndex(tab => tab.path === path)
      if (index > -1) {
        this.tabs.splice(index, 1)
      }
    },

    // 关闭其他标签页
    closeOthers(currentPath) {
      this.tabs = this.tabs.filter(tab => tab.path === currentPath)
    },

    // 关闭所有标签页
    closeAll() {
      this.tabs = []
    },

    // 更新标签页标题
    updateTabTitle(path, title) {
      const tab = this.tabs.find(tab => tab.path === path)
      if (tab) {
        tab.title = title
      }
    },

    // 初始化默认标签页
    initDefaultTab() {
      if (this.tabs.length === 0) {
        this.addTab({
          path: '/dashboard',
          name: 'Dashboard',
          meta: {
            title: '分析页',
            icon: 'mdi:view-dashboard'
          }
        })
      }
    }
  },

  persist: {
    key: 'vue-admin-tabs',
    storage: localStorage,
    paths: ['tabs']
  }
})
