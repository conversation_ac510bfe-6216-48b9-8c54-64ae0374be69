<template>
  <el-container class="app-layout" :class="{ 'is-dark': appStore.isDark }"
    <!-- 顶部导航栏 -->
    <el-header class="app-header" height="60px">
      <div class="header-left">
        <el-button 
          text 
          @click="appStore.toggleSidebar"
          class="sidebar-toggle"
        >
          <el-icon size="20">
            <Expand v-if="appStore.sidebarCollapsed" />
            <Fold v-else />
          </el-icon>
        </el-button>
        
        <div class="logo">
          <Icon icon="mdi:view-dashboard" class="logo-icon" />
          <span class="logo-text" v-if="!appStore.sidebarCollapsed">Vue Admin</span>
        </div>
      </div>
      
      <div class="header-right">
        <!-- 全屏切换 -->
        <el-tooltip :content="isFullscreen ? $t('layout.exitFullscreen') : $t('layout.fullscreen')">
          <el-button text @click="toggleFullscreen" class="header-btn">
            <el-icon size="18">
              <FullScreen />
            </el-icon>
          </el-button>
        </el-tooltip>
        
        <!-- 刷新页面 -->
        <el-tooltip :content="$t('layout.refresh')">
          <el-button text @click="refreshPage" class="header-btn">
            <el-icon size="18">
              <Refresh />
            </el-icon>
          </el-button>
        </el-tooltip>
        
        <!-- 语言切换 -->
        <el-dropdown @command="handleLanguageChange" class="header-dropdown">
          <el-button text class="header-btn">
            <Icon icon="mdi:translate" style="font-size: 18px;" />
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="zh-CN" :class="{ 'is-active': locale === 'zh-CN' }">
                {{ $t('language.zhCN') }}
              </el-dropdown-item>
              <el-dropdown-item command="en-US" :class="{ 'is-active': locale === 'en-US' }">
                {{ $t('language.enUS') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        
        <!-- 主题切换 -->
        <el-tooltip :content="appStore.isDark ? $t('theme.light') : $t('theme.dark')">
          <el-button text @click="appStore.toggleTheme" class="header-btn">
            <el-icon size="18">
              <Sunny v-if="appStore.isDark" />
              <Moon v-else />
            </el-icon>
          </el-button>
        </el-tooltip>
        
        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand" class="header-dropdown">
          <div class="user-info">
            <el-avatar :size="32" :src="authStore.user?.avatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="username">{{ authStore.user?.nickname || authStore.user?.username }}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                系统设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                {{ $t('login.logout') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>
    
    <el-container class="app-body">
      <!-- 侧边栏 -->
      <el-aside 
        class="app-sidebar"
        :width="appStore.sidebarCollapsed ? '64px' : '240px'"
      >
        <AppSidebar />
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="app-main">
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Expand,
  Fold,
  FullScreen,
  Refresh,
  Sunny,
  Moon,
  User,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { useAppStore } from '@/stores/app'
import { useAuthStore } from '@/stores/auth'
import AppSidebar from './AppSidebar.vue'

const router = useRouter()
const { locale, t } = useI18n()
const appStore = useAppStore()
const authStore = useAuthStore()

const isFullscreen = ref(false)

// 切换全屏
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 语言切换
const handleLanguageChange = (lang) => {
  locale.value = lang
  appStore.setLocale(lang)
  ElMessage.success(t('common.success'))
}

// 用户菜单操作
const handleUserCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        await authStore.logout()
        router.push('/login')
        ElMessage.success('退出成功')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

// 监听全屏状态变化
document.addEventListener('fullscreenchange', () => {
  isFullscreen.value = !!document.fullscreenElement
})
</script>

<style scoped>
.app-layout {
  height: 100vh;
  transition: all 0.3s ease;
}

.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: var(--el-bg-color);
  border-bottom: 1px solid var(--el-border-color-light);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  padding: 8px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.logo-icon {
  font-size: 24px;
  color: var(--el-color-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-btn {
  padding: 8px;
  border-radius: 6px;
}

.header-dropdown .el-button {
  padding: 8px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  cursor: pointer;
}

.username {
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.app-body {
  height: calc(100vh - 60px);
}

.app-sidebar {
  background: var(--el-bg-color);
  border-right: 1px solid var(--el-border-color-light);
  transition: width 0.3s ease;
}

.app-main {
  background: var(--el-bg-color-page);
  padding: 20px;
  overflow-y: auto;
}

.is-active {
  color: var(--el-color-primary);
  font-weight: 600;
}

/* 暗色主题 */
.is-dark {
  background: var(--el-bg-color);
}

.is-dark .app-header {
  background: var(--el-bg-color);
  border-bottom-color: var(--el-border-color);
}

.is-dark .app-sidebar {
  background: var(--el-bg-color);
  border-right-color: var(--el-border-color);
}

.is-dark .app-main {
  background: var(--el-bg-color-page);
}
</style>
