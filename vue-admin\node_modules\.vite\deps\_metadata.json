{"hash": "43b770e1", "configHash": "41ca168a", "lockfileHash": "c7755e66", "browserHash": "43cae8c8", "optimized": {"vue": {"src": "../../vue/dist/vue.runtime.esm-bundler.js", "file": "vue.js", "fileHash": "7fe1e405", "needsInterop": false}, "pinia": {"src": "../../pinia/dist/pinia.mjs", "file": "pinia.js", "fileHash": "52199701", "needsInterop": false}, "element-plus": {"src": "../../element-plus/es/index.mjs", "file": "element-plus.js", "fileHash": "d2d06d83", "needsInterop": false}, "@element-plus/icons-vue": {"src": "../../@element-plus/icons-vue/dist/index.js", "file": "@element-plus_icons-vue.js", "fileHash": "c0c49f63", "needsInterop": false}, "vue-i18n": {"src": "../../vue-i18n/dist/vue-i18n.mjs", "file": "vue-i18n.js", "fileHash": "8ece5df4", "needsInterop": false}, "@iconify/vue": {"src": "../../@iconify/vue/dist/iconify.mjs", "file": "@iconify_vue.js", "fileHash": "f90f8137", "needsInterop": false}, "vue-router": {"src": "../../vue-router/dist/vue-router.mjs", "file": "vue-router.js", "fileHash": "002387e8", "needsInterop": false}}, "chunks": {"chunk-CMNX4JC6": {"file": "chunk-CMNX4JC6.js"}, "chunk-OMQNJY6X": {"file": "chunk-OMQNJY6X.js"}}}