<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
        {{ $t('menu.logs') }}
      </h1>
      <n-button type="error" @click="handleClearLogs">
        <template #icon>
          <n-icon><trash-outline /></n-icon>
        </template>
        清空日志
      </n-button>
    </div>
    
    <!-- 搜索栏 -->
    <n-card>
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <n-input
          v-model:value="searchForm.user"
          :placeholder="$t('log.user')"
          clearable
        />
        <n-select
          v-model:value="searchForm.action"
          :placeholder="$t('log.action')"
          :options="actionOptions"
          clearable
        />
        <n-input
          v-model:value="searchForm.resource"
          :placeholder="$t('log.resource')"
          clearable
        />
        <n-date-picker
          v-model:value="searchForm.dateRange"
          type="daterange"
          clearable
          :placeholder="['开始日期', '结束日期']"
        />
        <div class="flex space-x-2">
          <n-button type="primary" @click="handleSearch">
            {{ $t('common.search') }}
          </n-button>
          <n-button @click="handleReset">
            {{ $t('common.reset') }}
          </n-button>
        </div>
      </div>
    </n-card>
    
    <!-- 日志表格 -->
    <n-card>
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </n-card>
    
    <!-- 日志详情模态框 -->
    <n-modal v-model:show="showDetailModal" preset="dialog" title="日志详情" style="width: 600px">
      <div v-if="selectedLog" class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400">用户</label>
            <p class="text-gray-900 dark:text-white">{{ selectedLog.user }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400">操作类型</label>
            <p class="text-gray-900 dark:text-white">{{ selectedLog.action }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400">操作资源</label>
            <p class="text-gray-900 dark:text-white">{{ selectedLog.resource }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400">IP地址</label>
            <p class="text-gray-900 dark:text-white">{{ selectedLog.ip }}</p>
          </div>
          <div class="col-span-2">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400">用户代理</label>
            <p class="text-gray-900 dark:text-white text-sm break-all">{{ selectedLog.userAgent }}</p>
          </div>
          <div class="col-span-2">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400">操作描述</label>
            <p class="text-gray-900 dark:text-white">{{ selectedLog.description }}</p>
          </div>
          <div class="col-span-2">
            <label class="text-sm font-medium text-gray-600 dark:text-gray-400">操作时间</label>
            <p class="text-gray-900 dark:text-white">{{ new Date(selectedLog.created_at).toLocaleString() }}</p>
          </div>
        </div>
      </div>
      
      <template #action>
        <n-button @click="showDetailModal = false">
          关闭
        </n-button>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  NCard,
  NButton,
  NIcon,
  NInput,
  NSelect,
  NDatePicker,
  NDataTable,
  NModal,
  NTag,
  useMessage,
  useDialog
} from 'naive-ui'
import {
  TrashOutline,
  EyeOutline
} from '@vicons/ionicons5'

const { t } = useI18n()
const message = useMessage()
const dialog = useDialog()

const loading = ref(false)
const showDetailModal = ref(false)
const selectedLog = ref(null)

const searchForm = reactive({
  user: '',
  action: null,
  resource: '',
  dateRange: null
})

const tableData = ref([])
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50]
})

const actionOptions = [
  { label: '登录', value: 'login' },
  { label: '登出', value: 'logout' },
  { label: '创建', value: 'create' },
  { label: '更新', value: 'update' },
  { label: '删除', value: 'delete' },
  { label: '查看', value: 'view' }
]

const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: t('log.user'),
    key: 'user'
  },
  {
    title: t('log.action'),
    key: 'action',
    render(row) {
      const actionMap = {
        login: { type: 'success', text: '登录' },
        logout: { type: 'info', text: '登出' },
        create: { type: 'success', text: '创建' },
        update: { type: 'warning', text: '更新' },
        delete: { type: 'error', text: '删除' },
        view: { type: 'info', text: '查看' }
      }
      const config = actionMap[row.action] || { type: 'default', text: row.action }
      return h(NTag, { type: config.type, size: 'small' }, { default: () => config.text })
    }
  },
  {
    title: t('log.resource'),
    key: 'resource'
  },
  {
    title: t('log.ip'),
    key: 'ip'
  },
  {
    title: t('log.description'),
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: t('log.createTime'),
    key: 'created_at',
    render(row) {
      return new Date(row.created_at).toLocaleString()
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          onClick: () => handleViewDetail(row)
        },
        { default: () => '详情', icon: () => h(NIcon, null, { default: () => h(EyeOutline) }) }
      )
    }
  }
]

const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    tableData.value = [
      {
        id: 1,
        user: 'admin',
        action: 'login',
        resource: '系统',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        description: '管理员登录系统',
        created_at: '2024-01-01T10:00:00Z'
      },
      {
        id: 2,
        user: 'admin',
        action: 'create',
        resource: '用户',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        description: '创建用户: user1',
        created_at: '2024-01-01T10:05:00Z'
      },
      {
        id: 3,
        user: 'user1',
        action: 'view',
        resource: '个人信息',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        description: '查看个人信息',
        created_at: '2024-01-01T11:00:00Z'
      }
    ]
    
    pagination.itemCount = tableData.value.length
  } catch (error) {
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  Object.assign(searchForm, {
    user: '',
    action: null,
    resource: '',
    dateRange: null
  })
  handleSearch()
}

const handlePageChange = (page) => {
  pagination.page = page
  loadData()
}

const handlePageSizeChange = (pageSize) => {
  pagination.pageSize = pageSize
  pagination.page = 1
  loadData()
}

const handleViewDetail = (row) => {
  selectedLog.value = row
  showDetailModal.value = true
}

const handleClearLogs = () => {
  dialog.warning({
    title: '确认清空',
    content: '确定要清空所有日志吗？此操作不可恢复！',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        message.success('清空成功')
        loadData()
      } catch (error) {
        message.error('清空失败')
      }
    }
  })
}

onMounted(() => {
  loadData()
})
</script>
